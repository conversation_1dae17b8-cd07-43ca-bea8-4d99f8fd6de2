import { request } from '../request';

/**
 * Get sales report data
 * @param params Report filter parameters
 */
export function fetchSalesReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.SalesReportResponse>>({
    url: '/api/v1/reports/sales',
    method: 'get',
    params
  });
}

/**
 * Get volume report data
 * @param params Report filter parameters
 */
export function fetchVolumeReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.VolumeReportResponse>>({
    url: '/api/v1/reports/volume',
    method: 'get',
    params
  });
}

/**
 * Get refunds report data
 * @param params Report filter parameters
 */
export function fetchRefundsReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.RefundReportResponse>>({
    url: '/api/v1/reports/refunds',
    method: 'get',
    params
  });
}

/**
 * Get order status report data
 * @param params Report filter parameters
 */
export function fetchOrderStatusReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.OrderStatusReportResponse>>({
    url: '/api/v1/reports/order-status',
    method: 'get',
    params
  });
}

/**
 * Export report data
 * @param data Export request data including filter parameters
 */
export function fetchExportReport(data: Api.Reports.ExportReportRequest & Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.ExportReportResponse>>({
    url: '/api/v1/reports/export',
    method: 'post',
    data
  });
}

/**
 * Helper function to get default report parameters
 * @param organisationId Organization ID (required)
 * @param daysBack Number of days to go back from today (default: 7)
 */
export function getDefaultReportParams(organisationId: number, daysBack: number = 7): Api.Reports.ReportFilterParams {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - daysBack);

  return {
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
    group_by: 'day',
    organisation_id: organisationId, // Pass single organization ID
    currency: 'USD'
  };
}

/**
 * Helper function to get last 7 days sales data for charts
 * @param organisationId Organization ID (required)
 */
export function fetchLast7DaysSalesData(organisationId: number) {
  const params = getDefaultReportParams(organisationId, 6); // Last 7 days including today
  return fetchSalesReport(params);
}

/**
 * Helper function to get last 7 days volume data for charts
 * @param organisationId Organization ID (required)
 */
export function fetchLast7DaysVolumeData(organisationId: number) {
  const params = getDefaultReportParams(organisationId, 6); // Last 7 days including today
  return fetchVolumeReport(params);
}

/**
 * Helper function to get current month sales data by country for pie charts
 * @param organisationId Organization ID (required)
 */
export function fetchCurrentMonthSalesByCountry(organisationId: number) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const params: Api.Reports.ReportFilterParams = {
    start_date: startOfMonth.toISOString().split('T')[0],
    end_date: now.toISOString().split('T')[0],
    group_by: 'month',
    organisation_id: organisationId, // Pass single organization ID
    currency: 'USD'
  };

  return fetchSalesReport(params);
}

/**
 * Helper function to get current month volume data by country for pie charts
 * @param organisationId Organization ID (required)
 */
export function fetchCurrentMonthVolumeByCountry(organisationId: number) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const params: Api.Reports.ReportFilterParams = {
    start_date: startOfMonth.toISOString().split('T')[0],
    end_date: now.toISOString().split('T')[0],
    group_by: 'month',
    organisation_id: organisationId // Pass single organization ID
  };

  return fetchVolumeReport(params);
}
