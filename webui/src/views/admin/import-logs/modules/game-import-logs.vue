<script setup lang="tsx">
import { computed, h, onMounted, onUnmounted, reactive, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag, NProgress, NSpin, NRadioGroup, NRadio, NInputNumber } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import {
  fetchSyncLogs,
  fetchSyncLogDetail,
  fetchRetrySync,
  fetchTriggerSync,
  fetchSyncProgress,
  fetchActiveSyncJobs,
  type SyncLogData,
  type SyncProgress,
  type ActiveSyncJob
} from '@/service/api/sync';

defineOptions({
  name: 'GameImportLogs'
});

// Use the SyncLogData interface from API service
type ImportLogData = SyncLogData & {
  // Add any additional fields if needed for UI display
  errorMessage?: string;
};

// View mode: 'logs' for sync logs, 'active' for active jobs
const viewMode = ref<'logs' | 'active'>('logs');

const searchForm = reactive({
  sync_type: '',
  status: '',
  per_page: 15
});

const loading = ref(false);
const retryLoading = ref<Record<number, boolean>>({});
const showDetailsModal = ref(false);
const selectedLog = ref<ImportLogData | null>(null);

// Sync progress modal state
const showSyncProgressModal = ref(false);
const syncProgress = ref<SyncProgress | null>(null);
const syncProgressTimer = ref<NodeJS.Timeout | null>(null);
const currentSyncBatchId = ref<string>('');
const hasBackgroundSync = ref(false);

// Sync configuration modal state
const showSyncConfigModal = ref(false);
const syncConfigForm = reactive({
  sync_type: 'product_sync',
  incremental: false,
  batch_size: 100,
  timeout: 1800
});
const syncConfigLoading = ref(false);

// Keep track of timer ID for debugging
let lastTimerId: NodeJS.Timeout | null = null;
const tableData = ref<ImportLogData[]>([]);

// Active jobs data and auto-refresh
const activeJobsData = ref<ActiveSyncJob[]>([]);
const autoRefreshEnabled = ref(true);
const refreshInterval = ref(5); // seconds
const autoRefreshTimer = ref<NodeJS.Timeout | null>(null);
const isAutoRefreshing = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 15,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
    loadSyncLogs();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    searchForm.per_page = pageSize;
    loadSyncLogs();
  }
});

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// Load sync logs from API
async function loadSyncLogs() {
  loading.value = true;
  try {
    const { data } = await fetchSyncLogs({
      per_page: pagination.pageSize,
      sync_type: searchForm.sync_type || undefined,
      status: searchForm.status || undefined
    });

    if (data && data.success && data.data) {
      tableData.value = data.data.data;
      pagination.total = data.data.meta.total;
      pagination.page = data.data.meta.current_page;
    }
  } catch (error) {
    console.error('Failed to load sync logs:', error);
    window.$message?.error($t('page.admin.loadSyncLogsFailed'));
  } finally {
    loading.value = false;
  }
}

// Load active sync jobs from API
async function loadActiveSyncJobs(showLoading = true) {
  // Only show loading for initial load or manual refresh
  if (showLoading) {
    loading.value = true;
  } else {
    // Show subtle auto-refresh indicator
    isAutoRefreshing.value = true;
  }

  try {
    const { data } = await fetchActiveSyncJobs();

    if (data && data.success && data.data) {
      activeJobsData.value = data.data.active_jobs;
    }
  } catch (error) {
    console.error('Failed to load active sync jobs:', error);
    // Only show error message for manual refresh, not auto-refresh
    if (showLoading) {
      window.$message?.error($t('page.admin.loadActiveJobsFailed'));
    }
  } finally {
    if (showLoading) {
      loading.value = false;
    } else {
      // Hide auto-refresh indicator after a short delay
      setTimeout(() => {
        isAutoRefreshing.value = false;
      }, 500);
    }
  }
}

// Switch view mode
function switchViewMode(mode: 'logs' | 'active') {
  viewMode.value = mode;

  // Stop auto-refresh when switching away from active mode
  if (mode !== 'active') {
    stopAutoRefresh();
  }

  // Load appropriate data
  if (mode === 'logs') {
    loadSyncLogs();
  } else {
    // Show loading for initial switch to active mode
    loadActiveSyncJobs(true);
    if (autoRefreshEnabled.value) {
      startAutoRefresh();
    }
  }
}

// Start auto-refresh for active jobs
function startAutoRefresh() {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value);
  }

  autoRefreshTimer.value = setInterval(() => {
    if (viewMode.value === 'active' && autoRefreshEnabled.value) {
      // Auto-refresh without showing loading state to avoid flickering
      loadActiveSyncJobs(false);
    }
  }, refreshInterval.value * 1000);
}

// Stop auto-refresh
function stopAutoRefresh() {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value);
    autoRefreshTimer.value = null;
  }
}

// Toggle auto-refresh
function toggleAutoRefresh() {
  autoRefreshEnabled.value = !autoRefreshEnabled.value;

  if (autoRefreshEnabled.value && viewMode.value === 'active') {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}

// Statistics calculation based on API data structure
const statistics = computed(() => {
  if (viewMode.value === 'active') {
    const total = activeJobsData.value.length;
    const running = activeJobsData.value.filter(item => item.status === 'running').length;
    const pending = activeJobsData.value.filter(item => item.status === 'pending').length;

    return {
      total,
      completed: 0,
      failed: 0,
      running,
      pending,
      successRate: 0
    };
  }

  const total = tableData.value.length;
  const completed = tableData.value.filter(item => item.status === 'completed').length;
  const failed = tableData.value.filter(item => item.status === 'failed').length;
  const running = tableData.value.filter(item => item.status === 'running').length;

  return {
    total,
    completed,
    failed,
    running,
    successRate: total > 0 ? Math.round((completed / total) * 100) : 0
  };
});

// Active jobs table columns
const activeJobsColumns = computed<DataTableColumns<ActiveSyncJob>>(() => [
  {
    title: $t('page.admin.jobId'),
    key: 'job_id',
    width: isMobile.value ? 140 : 180,
    minWidth: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.admin.syncProgress.batchId'),
    key: 'batch_id',
    width: isMobile.value ? 140 : 180,
    minWidth: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.admin.importType'),
    key: 'sync_type',
    width: isMobile.value ? 80 : 120,
    minWidth: 70,
    render: row => {
      const typeMap: Record<string, string> = {
        product_sync: $t('page.admin.syncTypes.product'),
        inventory_sync: $t('page.admin.syncTypes.inventory'),
        price_sync: $t('page.admin.syncTypes.price')
      };
      return h('span', typeMap[row.sync_type] || row.sync_type);
    }
  },
  {
    title: $t('page.admin.progress'),
    key: 'percentage',
    width: isMobile.value ? 80 : 100,
    minWidth: 70,
    render: row => {
      return h('span', `${row.percentage}%`);
    }
  },
  {
    title: $t('page.admin.syncProgress.status'),
    key: 'status',
    width: isMobile.value ? 80 : 100,
    minWidth: 70,
    render: row => {
      const statusColorMap: Record<string, string> = {
        pending: 'default',
        running: 'info',
        completed: 'success',
        failed: 'error'
      };
      const statusLabelMap: Record<string, string> = {
        pending: $t('page.admin.syncProgress.statusLabels.pending'),
        running: $t('page.admin.syncProgress.statusLabels.running'),
        completed: $t('page.admin.syncProgress.statusLabels.completed'),
        failed: $t('page.admin.syncProgress.statusLabels.failed')
      };
      return h(
        NTag,
        {
          type: statusColorMap[row.status] as any,
          size: isMobile.value ? 'small' : 'medium'
        },
        {
          default: () => statusLabelMap[row.status] || row.status
        }
      );
    }
  },
  {
    title: isMobile.value ? $t('page.admin.startedShort') : $t('page.admin.syncProgress.startTime'),
    key: 'started_at',
    width: isMobile.value ? 120 : 160,
    minWidth: 100,
    render: row => {
      if (!row.started_at) {
        return h('span', '-');
      }
      const date = new Date(row.started_at);
      const formatted = isMobile.value
        ? date.toLocaleDateString()
        : date.toLocaleString();
      return h('span', { title: date.toLocaleString() }, formatted);
    }
  }
]);

// Sync logs table columns
const columns = computed<DataTableColumns<ImportLogData>>(() => [
  {
    title: $t('page.admin.syncProgress.batchId'),
    key: 'batch_id',
    width: isMobile.value ? 140 : 180,
    minWidth: 120,
    sorter: true,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.admin.importType'),
    key: 'sync_type',
    width: isMobile.value ? 80 : 120,
    minWidth: 70,
    sorter: true,
    render: row => {
      const typeMap: Record<string, string> = {
        product_sync: $t('page.admin.syncTypes.product'),
        inventory_sync: $t('page.admin.syncTypes.inventory'),
        price_sync: $t('page.admin.syncTypes.price')
      };
      return h('span', typeMap[row.sync_type] || row.sync_type);
    }
  },
  {
    title: isMobile.value ? $t('page.admin.startedShort') : $t('page.admin.syncProgress.startTime'),
    key: 'started_at',
    width: isMobile.value ? 120 : 160,
    minWidth: 100,
    sorter: true,
    render: row => {
      const date = new Date(row.started_at);
      const formatted = isMobile.value
        ? date.toLocaleDateString()
        : date.toLocaleString();
      return h('span', { title: date.toLocaleString() }, formatted);
    }
  },
  {
    title: isMobile.value ? $t('page.admin.recordsShort') : $t('page.admin.syncProgress.totalRecords'),
    key: 'total_records',
    width: isMobile.value ? 80 : 100,
    minWidth: 70,
    sorter: true,
    render: row => {
      return h('span', `${row.total_records}`);
    }
  },
  {
    title: $t('page.admin.progress'),
    key: 'progress_percentage',
    width: isMobile.value ? 80 : 100,
    minWidth: 70,
    render: row => {
      return h('span', `${row.progress_percentage}%`);
    }
  },
  {
    title: $t('page.admin.syncProgress.status'),
    key: 'status',
    width: isMobile.value ? 80 : 100,
    minWidth: 70,
    render: row => {
      const statusColorMap: Record<string, string> = {
        pending: 'default',
        running: 'info',
        completed: 'success',
        failed: 'error'
      };
      const statusLabelMap: Record<string, string> = {
        pending: $t('page.admin.syncProgress.statusLabels.pending'),
        running: $t('page.admin.syncProgress.statusLabels.running'),
        completed: $t('page.admin.syncProgress.statusLabels.completed'),
        failed: $t('page.admin.syncProgress.statusLabels.failed')
      };
      return h(
        NTag,
        {
          type: statusColorMap[row.status] as any,
          size: isMobile.value ? 'small' : 'medium'
        },
        {
          default: () => statusLabelMap[row.status] || row.status
        }
      );
    }
  },
  {
    title: $t('common.operate'),
    key: 'actions',
    width: isMobile.value ? 100 : 120,
    minWidth: 80,
    fixed: isMobile.value ? 'right' : undefined,
    render: row => {
      const canRetry = row.status === 'failed';

      return h('div', { class: isMobile.value ? 'flex flex-col gap-1' : 'flex gap-2' }, [
        h(ButtonIcon, {
          icon: 'mdi:eye',
          tooltipContent: $t('page.admin.viewDetails'),
          class: 'text-primary',
          size: isMobile.value ? 'small' : 'medium',
          onClick: () => handleViewDetails(row)
        }),
        canRetry &&
          h(
            NButton,
            {
              size: isMobile.value ? 'tiny' : 'small',
              type: 'primary',
              loading: retryLoading.value[row.id],
              onClick: () => handleRetry(row)
            },
            {
              default: () => $t('page.admin.retry'),
              icon: () => h(SvgIcon, { icon: 'mdi:refresh' })
            }
          )
      ]);
    }
  }
]);

// Search function
function handleSearch() {
  if (viewMode.value === 'logs') {
    pagination.page = 1;
    loadSyncLogs();
  } else {
    // Show loading for manual search
    loadActiveSyncJobs(true);
  }
}

// Reset search form
function handleReset() {
  Object.assign(searchForm, {
    sync_type: '',
    status: '',
    per_page: 15
  });
  if (viewMode.value === 'logs') {
    pagination.page = 1;
    loadSyncLogs();
  } else {
    // Show loading for manual reset
    loadActiveSyncJobs(true);
  }
}

// Refresh data
function handleRefresh() {
  if (viewMode.value === 'logs') {
    loadSyncLogs();
  } else {
    // Show loading for manual refresh
    loadActiveSyncJobs(true);
  }
}

// Show sync configuration modal
function handleShowSyncConfig() {
  showSyncConfigModal.value = true;
}

// Close sync configuration modal and reset form
function handleCloseSyncConfig() {
  showSyncConfigModal.value = false;
  // Reset form to default values
  Object.assign(syncConfigForm, {
    sync_type: 'product_sync',
    incremental: false,
    batch_size: 100,
    timeout: 1800
  });
}

// Trigger new sync with configuration
async function handleTriggerSyncWithConfig() {
  syncConfigLoading.value = true;
  try {
    const { data } = await fetchTriggerSync({
      sync_type: syncConfigForm.sync_type,
      incremental: syncConfigForm.incremental,
      batch_size: syncConfigForm.batch_size,
      timeout: syncConfigForm.timeout
    });

    if (data && data.success && data.data) {
      window.$message?.success($t('page.admin.syncProgress.syncStarted'));
      currentSyncBatchId.value = data.data.batch_id;

      // Close config modal
      showSyncConfigModal.value = false;

      // Show progress modal and start monitoring
      showSyncProgressModal.value = true;
      startSyncProgressMonitoring(data.data.batch_id);
    }
  } catch (error) {
    console.error('Failed to trigger sync:', error);
    window.$message?.error($t('page.admin.syncProgress.triggerSyncFailed'));
  } finally {
    syncConfigLoading.value = false;
  }
}

// Legacy trigger sync function (kept for compatibility)
async function handleTriggerSync() {
  // Open configuration modal instead of directly triggering sync
  handleShowSyncConfig();
}

// Start sync progress monitoring
async function startSyncProgressMonitoring(batchId: string) {
  // Clear any existing timer
  if (syncProgressTimer.value) {
    clearTimeout(syncProgressTimer.value);
    syncProgressTimer.value = null;
  }
  if (lastTimerId) {
    clearTimeout(lastTimerId);
    lastTimerId = null;
  }

  console.log('Sync progress monitoring started for batch:', batchId);

  // Start the recursive polling directly
  await fetchSyncProgressData(batchId, true);
}

// Fetch sync progress data with recursive setTimeout
async function fetchSyncProgressData(batchId: string, isInitialCall = false) {
  // Check if monitoring is still active (skip check for initial call)
  if (!isInitialCall && syncProgressTimer.value === null) {
    console.log('Sync monitoring is not active, stopping fetch for batch:', batchId);
    return;
  }

  console.log('Fetching sync progress for batch:', batchId);

  try {
    const { data } = await fetchSyncProgress({ batch_id: batchId });
    console.log('Sync progress response:', data);

    if (data && data.success && data.data) {
      syncProgress.value = data.data;

      // Check if sync is completed or failed
      if (data.data.status === 'completed' || data.data.status === 'failed') {
        // Sync finished - stop monitoring
        stopSyncProgressMonitoring();
        hasBackgroundSync.value = false;

        if (data.data.status === 'completed') {
          window.$message?.success($t('page.admin.syncProgress.syncCompleted'), { duration: 3000 });
          // Refresh the sync logs list
          setTimeout(() => {
            loadSyncLogs();
          }, 1000);

          // Close modal after 3 seconds for completed sync
          setTimeout(() => {
            if (showSyncProgressModal.value) {
              closeSyncProgressModal();
            }
          }, 3000);
        } else {
          window.$message?.error($t('page.admin.syncProgress.syncFailed'));
        }

        // Don't schedule next call - sync is finished
        return;
      } else {
        // Sync is still in progress - schedule next call
        scheduleNextProgressCheck(batchId);
      }
    } else if (data && data.success === false) {
      // API explicitly returned error response
      console.error('Sync progress API returned error:', data);
      stopSyncProgressMonitoring();
      hasBackgroundSync.value = false;

      // Update progress to show error state - create progress object if it doesn't exist
      if (!syncProgress.value) {
        syncProgress.value = {
          job_id: '',
          batch_id: batchId,
          status: 'failed',
          progress_percentage: 0,
          total_records: 0,
          processed_records: 0,
          success_records: 0,
          failed_records: 0,
          started_at: new Date().toISOString()
        };
      } else {
        syncProgress.value.status = 'failed';
      }

      window.$message?.error($t('page.admin.syncProgress.syncFailed'));
      // Don't schedule next call - error occurred
      return;
    } else if (data && data.success === true && !data.data) {
      // API returned success but no data - this might be normal during initial sync setup
      console.log('Sync progress data not yet available, but API returned success:', data);

      // If we don't have progress data yet, create a placeholder
      if (!syncProgress.value) {
        syncProgress.value = {
          job_id: '',
          batch_id: batchId,
          status: 'pending',
          progress_percentage: 0,
          total_records: 0,
          processed_records: 0,
          success_records: 0,
          failed_records: 0,
          started_at: new Date().toISOString()
        };
      }

      // Continue polling - schedule next call
      scheduleNextProgressCheck(batchId);
    } else {
      // Unexpected response format - stop polling to avoid infinite loops
      console.warn('Unexpected API response format:', data);
      stopSyncProgressMonitoring();
      hasBackgroundSync.value = false;

      // Update progress to show error state - create progress object if it doesn't exist
      if (!syncProgress.value) {
        syncProgress.value = {
          job_id: '',
          batch_id: batchId,
          status: 'failed',
          progress_percentage: 0,
          total_records: 0,
          processed_records: 0,
          success_records: 0,
          failed_records: 0,
          started_at: new Date().toISOString()
        };
      } else {
        syncProgress.value.status = 'failed';
      }

      window.$message?.error($t('page.admin.syncProgress.syncFailed'));
      return;
    }
  } catch (error) {
    console.error('Failed to fetch sync progress:', error);
    console.error('Error details:', {
      status: (error as any)?.status || (error as any)?.response?.status,
      statusText: (error as any)?.response?.statusText,
      message: (error as any)?.message,
      isNotFound: (error as any)?.isNotFound,
      batchId
    });

    // Force stop monitoring and clean up all state
    stopSyncProgressMonitoring();
    hasBackgroundSync.value = false;

    // Update progress to show error state - create progress object if it doesn't exist
    if (!syncProgress.value) {
      syncProgress.value = {
        job_id: '',
        batch_id: batchId,
        status: 'failed',
        progress_percentage: 0,
        total_records: 0,
        processed_records: 0,
        success_records: 0,
        failed_records: 0,
        started_at: new Date().toISOString()
      };
    } else {
      syncProgress.value.status = 'failed';
    }

    // Show error message based on error type
    const is404Error = (error as any)?.status === 404 ||
                      (error as any)?.response?.status === 404 ||
                      (error as any)?.isNotFound;

    const errorMessage = is404Error
      ? $t('page.admin.syncProgress.syncNotFound')
      : $t('page.admin.syncProgress.syncFailed');

    window.$message?.error(errorMessage);

    console.log('Sync monitoring stopped due to error. Timer should be cleared.');
    // Don't schedule next call - error occurred
  }
}

// Schedule next progress check using setTimeout
function scheduleNextProgressCheck(batchId: string) {
  const timerId = setTimeout(() => {
    // Check if monitoring is still active before making the next call
    if (syncProgressTimer.value === timerId) {
      fetchSyncProgressData(batchId, false);
    } else {
      console.log('Sync monitoring was stopped, skipping scheduled call for batch:', batchId);
    }
  }, 3000);

  syncProgressTimer.value = timerId;
  lastTimerId = timerId;
}

// Stop sync progress monitoring
function stopSyncProgressMonitoring() {
  // Clear the main timer
  if (syncProgressTimer.value) {
    clearTimeout(syncProgressTimer.value);
    syncProgressTimer.value = null;
  }

  // Clear the backup timer reference
  if (lastTimerId) {
    clearTimeout(lastTimerId);
    lastTimerId = null;
  }

  console.log('Sync progress monitoring stopped - all timers cleared');
}

// Close sync progress modal
function closeSyncProgressModal() {
  showSyncProgressModal.value = false;

  // If sync is still running, keep it in background
  if (syncProgress.value && (syncProgress.value.status === 'running' || syncProgress.value.status === 'pending')) {
    hasBackgroundSync.value = true;
    // Keep monitoring in background
  } else {
    // Sync is completed or failed, clean up everything
    stopSyncProgressMonitoring();
    syncProgress.value = null;
    currentSyncBatchId.value = '';
    hasBackgroundSync.value = false;
  }
}

// Show sync progress modal (for background sync)
function showSyncProgressFromBackground() {
  if (hasBackgroundSync.value && currentSyncBatchId.value) {
    showSyncProgressModal.value = true;
  }
}

// Manually stop sync
function handleStopSync() {
  window.$dialog?.warning({
    title: $t('page.admin.syncProgress.stopSync'),
    content: $t('page.admin.syncProgress.confirmStopSync'),
    positiveText: $t('page.admin.syncProgress.stopSync'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      console.log('User manually stopped sync for batch:', currentSyncBatchId.value);

      // Stop monitoring and clean up state
      stopSyncProgressMonitoring();
      hasBackgroundSync.value = false;

      // Update progress to show stopped state
      if (syncProgress.value) {
        syncProgress.value.status = 'failed';
        // Add a note that it was manually stopped
        (syncProgress.value as any).manually_stopped = true;
      }

      // Show stopped message
      window.$message?.info($t('page.admin.syncProgress.syncStopped'));

      // Close modal after a short delay
      setTimeout(() => {
        closeSyncProgressModal();
      }, 1000);
    }
  });
}

// Retry failed sync
async function handleRetry(row: ImportLogData) {
  retryLoading.value[row.id] = true;

  try {
    const { data } = await fetchRetrySync(row.id);

    if (data && data.success) {
      window.$message?.success($t('page.admin.retryStartedSuccess'));
      // Refresh the list
      loadSyncLogs();
    }
  } catch (error) {
    console.error('Failed to retry sync:', error);
    window.$message?.error($t('page.admin.retryFailed'));
  } finally {
    retryLoading.value[row.id] = false;
  }
}

// View sync log details
async function handleViewDetails(row: ImportLogData) {
  try {
    const { data } = await fetchSyncLogDetail(row.id);
    if (data && data.success && data.data) {
      selectedLog.value = data.data;
      showDetailsModal.value = true;
    }
  } catch (error) {
    console.error('Failed to load sync log details:', error);
    window.$message?.error($t('page.admin.loadDetailsFailed'));
  }
}

// Initialize data on component mount
onMounted(() => {
  if (viewMode.value === 'logs') {
    loadSyncLogs();
  } else {
    // Show loading for initial mount
    loadActiveSyncJobs(true);
    if (autoRefreshEnabled.value) {
      startAutoRefresh();
    }
  }
});

onUnmounted(() => {
  // Clean up sync progress monitoring timer
  stopSyncProgressMonitoring();
  // Clean up auto-refresh timer
  stopAutoRefresh();
});
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <h3 class="text-16px font-semibold">{{ $t('page.admin.dataSyncLogs') }}</h3>

        <!-- View mode selector -->
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.viewMode') }}:</span>
          <NButtonGroup>
            <NButton
              :type="viewMode === 'logs' ? 'primary' : 'default'"
              size="small"
              :loading="loading && viewMode === 'logs'"
              @click="switchViewMode('logs')"
            >
              <template #icon>
                <SvgIcon icon="mdi:history" />
              </template>
              {{ $t('page.admin.logsMode') }}
            </NButton>
            <NButton
              :type="viewMode === 'active' ? 'primary' : 'default'"
              size="small"
              :loading="loading && viewMode === 'active'"
              @click="switchViewMode('active')"
            >
              <template #icon>
                <SvgIcon icon="mdi:play-circle" />
              </template>
              {{ $t('page.admin.activeJobsMode') }}
            </NButton>
          </NButtonGroup>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <!-- Auto-refresh controls for active jobs mode -->
        <div v-if="viewMode === 'active'" :class="isMobile ? 'flex flex-col gap-1' : 'flex items-center gap-2'">
          <NButton
            :type="autoRefreshEnabled ? 'primary' : 'default'"
            size="small"
            ghost
            @click="toggleAutoRefresh"
          >
            <template #icon>
              <SvgIcon :icon="autoRefreshEnabled ? 'mdi:pause' : 'mdi:play'" />
            </template>
            {{ isMobile ? '' : $t('page.admin.autoRefresh') }}
          </NButton>

          <NSelect
            v-model:value="refreshInterval"
            :options="[
              { label: `3 ${$t('page.admin.seconds')}`, value: 3 },
              { label: `5 ${$t('page.admin.seconds')}`, value: 5 },
              { label: `10 ${$t('page.admin.seconds')}`, value: 10 },
              { label: `30 ${$t('page.admin.seconds')}`, value: 30 }
            ]"
            size="small"
            :style="isMobile ? 'width: 80px' : 'width: 120px'"
            @update:value="() => { if (autoRefreshEnabled && viewMode === 'active') startAutoRefresh(); }"
          />

          <!-- Auto-refresh status indicator -->
          <div v-if="autoRefreshEnabled && viewMode === 'active'" class="flex items-center gap-1">
            <SvgIcon
              icon="mdi:sync"
              :class="[
                'text-xs',
                isAutoRefreshing ? 'text-blue-500 animate-spin' : 'text-green-500',
                autoRefreshEnabled ? 'animate-pulse' : ''
              ]"
            />
            <span v-if="!isMobile" :class="[
              'text-xs transition-colors duration-300',
              isAutoRefreshing ? 'text-blue-600' : 'text-green-600'
            ]">
              {{ isAutoRefreshing ? $t('page.admin.refreshing') : $t('page.admin.autoRefresh') }}
            </span>
          </div>
        </div>

        <!-- Background sync indicator -->
        <NButton
          v-if="hasBackgroundSync"
          type="info"
          ghost
          @click="showSyncProgressFromBackground"
        >
          <template #icon>
            <SvgIcon icon="mdi:sync" class="animate-spin" />
          </template>
          {{ $t('page.admin.syncProgress.viewProgress') }}
        </NButton>

        <!-- Trigger sync button - only show in logs mode -->
        <NButton
          v-if="viewMode === 'logs'"
          type="primary"
          :loading="loading"
          :disabled="hasBackgroundSync"
          @click="handleTriggerSync"
        >
          <template #icon>
            <SvgIcon icon="mdi:play" />
          </template>
          {{ $t('page.admin.startSync') }}
        </NButton>

        <NButton :loading="loading" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('page.admin.refresh') }}
        </NButton>
      </div>
    </div>

    <!-- Compact sync logs filter - only show in logs mode -->
    <div v-if="viewMode === 'logs'" class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- Search fields and buttons -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.syncTypeFilter') }}</span>
          <NSelect
            v-model:value="searchForm.sync_type"
            :placeholder="$t('page.admin.selectSyncType')"
            :options="[
              { label: $t('page.admin.syncTypes.product') + ' ' + $t('page.admin.syncProgress.title'), value: 'product_sync' },
              { label: $t('page.admin.syncTypes.inventory') + ' ' + $t('page.admin.syncProgress.title'), value: 'inventory_sync' },
              { label: $t('page.admin.syncTypes.price') + ' ' + $t('page.admin.syncProgress.title'), value: 'price_sync' }
            ]"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-24 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.syncProgress.status') }}</span>
          <NSelect
            v-model:value="searchForm.status"
            :placeholder="$t('page.admin.selectStatus')"
            :options="[
              { label: $t('page.admin.syncProgress.statusLabels.pending'), value: 'pending' },
              { label: $t('page.admin.syncProgress.statusLabels.running'), value: 'running' },
              { label: $t('page.admin.syncProgress.statusLabels.completed'), value: 'completed' },
              { label: $t('page.admin.syncProgress.statusLabels.failed'), value: 'failed' }
            ]"
            size="small"
            clearable
          />
        </div>

        <!-- Action buttons -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('page.admin.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('page.admin.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- Statistics cards -->
    <div :class="viewMode === 'active' ? 'grid grid-cols-1 gap-4 md:grid-cols-3' : 'grid grid-cols-2 gap-4 md:grid-cols-4'">
      <div class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800 transition-all duration-200 hover:shadow-md">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ viewMode === 'active' ? $t('page.admin.activeJobs') : $t('page.admin.totalSyncs') }}
            </p>
            <p class="text-2xl text-gray-900 font-bold dark:text-white">{{ statistics.total }}</p>
          </div>
          <SvgIcon
            :icon="viewMode === 'active' ? 'mdi:play-circle' : 'mdi:database'"
            class="text-2xl text-blue-500"
          />
        </div>
      </div>

      <!-- Logs mode cards -->
      <div v-if="viewMode === 'logs'" class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800 transition-all duration-200 hover:shadow-md">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.syncProgress.statusLabels.completed') }}</p>
            <p class="text-2xl text-green-600 font-bold">{{ statistics.completed }}</p>
          </div>
          <SvgIcon icon="mdi:check-circle" class="text-2xl text-green-500" />
        </div>
      </div>

      <div v-if="viewMode === 'logs'" class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800 transition-all duration-200 hover:shadow-md">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.syncProgress.statusLabels.failed') }}</p>
            <p class="text-2xl text-red-600 font-bold">{{ statistics.failed }}</p>
          </div>
          <SvgIcon icon="mdi:alert-circle" class="text-2xl text-red-500" />
        </div>
      </div>

      <!-- Active mode cards -->
      <div v-if="viewMode === 'active'" class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800 transition-all duration-200 hover:shadow-md">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.syncProgress.statusLabels.pending') }}</p>
            <p class="text-2xl text-yellow-600 font-bold">{{ statistics.pending || 0 }}</p>
          </div>
          <SvgIcon icon="mdi:clock-outline" class="text-2xl text-yellow-500" />
        </div>
      </div>

      <!-- Running jobs card - shown in both modes -->
      <div class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800 transition-all duration-200 hover:shadow-md">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $t('page.admin.syncProgress.statusLabels.running') }}</p>
            <p class="text-2xl text-blue-600 font-bold">{{ statistics.running }}</p>
          </div>
          <SvgIcon icon="mdi:play-circle" class="text-2xl text-blue-500" />
        </div>
      </div>
    </div>

    <!-- Empty state for active jobs -->
    <div v-if="viewMode === 'active' && activeJobsData.length === 0 && !loading" class="text-center py-12">
      <div class="flex flex-col items-center">
        <SvgIcon icon="mdi:sleep" class="text-6xl text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">{{ $t('page.admin.noActiveJobs') }}</h3>
        <p class="text-sm text-gray-500 dark:text-gray-500 mb-4">{{ $t('page.admin.noActiveJobsDesc') }}</p>
        <NButton type="primary" ghost @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('page.admin.refresh') }}
        </NButton>
      </div>
    </div>

    <!-- Loading skeleton for active jobs -->
    <div v-else-if="viewMode === 'active' && loading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div class="space-y-2 flex-1">
            <div class="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
            <div class="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
          <div class="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>

    <NDataTable
      v-else
      :columns="viewMode === 'active' ? activeJobsColumns : columns"
      :data="viewMode === 'active' ? activeJobsData : tableData"
      :loading="loading"
      :pagination="viewMode === 'logs' ? pagination : false"
      :bordered="false"
      :size="isMobile ? 'small' : 'medium'"
      flex-height
      :class="isMobile ? 'h-400px mobile-table' : 'h-500px'"
      :scroll-x="isMobile ? 800 : undefined"
    />

    <!-- Sync details modal -->
    <NModal
      v-model:show="showDetailsModal"
      preset="dialog"
      :title="$t('page.admin.syncDetails')"
      :negative-text="$t('page.admin.close')"
      :style="isMobile ? 'width: calc(100vw - 32px); max-width: 100%;' : 'width: 600px;'"
    >
      <div v-if="selectedLog" class="space-y-4">
        <NDescriptions :column="isMobile ? 1 : 2" bordered>
          <NDescriptionsItem :label="$t('page.admin.syncProgress.batchId')">
            {{ selectedLog.batch_id }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.importType')">
            {{ selectedLog.sync_type }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.syncProgress.startTime')">
            {{ new Date(selectedLog.started_at).toLocaleString() }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.completedAt')">
            {{ selectedLog.completed_at ? new Date(selectedLog.completed_at).toLocaleString() : $t('common.notAvailable') }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.syncProgress.totalRecords')">
            {{ selectedLog.total_records }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.syncProgress.successRecords')">
            <span class="text-green-600">{{ selectedLog.success_records }}</span>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.syncProgress.failedRecords')">
            <span class="text-red-600">{{ selectedLog.failed_records }}</span>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.progress')">
            {{ selectedLog.progress_percentage }}%
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.admin.syncProgress.status')">
            <NTag
              :type="
                selectedLog.status === 'completed'
                  ? 'success'
                  : selectedLog.status === 'failed'
                    ? 'error'
                    : selectedLog.status === 'running'
                      ? 'info'
                      : 'default'
              "
            >
              {{
                selectedLog.status === 'completed'
                  ? $t('page.admin.syncProgress.statusLabels.completed')
                  : selectedLog.status === 'failed'
                  ? $t('page.admin.syncProgress.statusLabels.failed')
                  : selectedLog.status === 'running'
                  ? $t('page.admin.syncProgress.statusLabels.running')
                  : $t('page.admin.syncProgress.statusLabels.pending')
              }}
            </NTag>
          </NDescriptionsItem>
        </NDescriptions>

        <!-- Show failed records if any -->
        <div v-if="selectedLog.records && selectedLog.records.length > 0" class="space-y-2">
          <h4 class="text-14px font-medium">{{ $t('page.admin.failedRecordsTitle') }}</h4>
          <div class="max-h-200px overflow-y-auto space-y-2">
            <div
              v-for="record in selectedLog.records"
              :key="record.id"
              class="border border-red-200 rounded bg-red-50 p-3 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
            >
              <div class="text-sm font-medium">{{ record.error_message }}</div>
              <div class="text-xs mt-1 opacity-75">{{ new Date(record.created_at).toLocaleString() }}</div>
            </div>
          </div>
        </div>
      </div>
    </NModal>

    <!-- Sync progress modal -->
    <NModal
      v-model:show="showSyncProgressModal"
      preset="dialog"
      :title="$t('page.admin.syncProgress.title')"
      :closable="false"
      :mask-closable="false"
      :style="isMobile ? 'width: calc(100vw - 32px); max-width: 100%;' : 'width: 500px;'"
    >
      <div v-if="syncProgress" class="space-y-4">
        <!-- Progress bar -->
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium">{{ $t('page.admin.syncProgress.title') }}</span>
            <span class="text-sm text-gray-600">{{ syncProgress.progress_percentage }}%</span>
          </div>
          <NProgress
            type="line"
            :percentage="syncProgress.progress_percentage"
            :status="syncProgress.status === 'failed' ? 'error' : syncProgress.status === 'completed' ? 'success' : 'info'"
            :show-indicator="false"
            :height="8"
          />
        </div>

        <!-- Status info -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.status') }}:</span>
            <NTag
              :type="
                syncProgress.status === 'completed'
                  ? 'success'
                  : syncProgress.status === 'failed'
                  ? 'error'
                  : syncProgress.status === 'running'
                  ? 'info'
                  : 'default'
              "
              size="small"
              class="ml-2"
            >
              {{
                syncProgress.status === 'completed'
                  ? $t('page.admin.syncProgress.statusLabels.completed')
                  : syncProgress.status === 'failed'
                  ? $t('page.admin.syncProgress.statusLabels.failed')
                  : syncProgress.status === 'running'
                  ? $t('page.admin.syncProgress.statusLabels.running')
                  : $t('page.admin.syncProgress.statusLabels.pending')
              }}
            </NTag>
          </div>
          <div>
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.batchId') }}:</span>
            <span class="ml-2 font-mono text-xs">{{ syncProgress.batch_id }}</span>
          </div>
        </div>

        <!-- Records info -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.totalRecords') }}:</span>
            <span class="ml-2 font-medium">{{ syncProgress.total_records }}</span>
          </div>
          <div>
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.processedRecords') }}:</span>
            <span class="ml-2 font-medium">{{ syncProgress.processed_records }}</span>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.successRecords') }}:</span>
            <span class="ml-2 font-medium text-green-600">{{ syncProgress.success_records }}</span>
          </div>
          <div>
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.failedRecords') }}:</span>
            <span class="ml-2 font-medium text-red-600">{{ syncProgress.failed_records }}</span>
          </div>
        </div>

        <!-- Time info -->
        <div class="text-sm">
          <div class="mb-1">
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.startTime') }}:</span>
            <span class="ml-2">{{ new Date(syncProgress.started_at).toLocaleString() }}</span>
          </div>
          <div v-if="syncProgress.estimated_completion">
            <span class="text-gray-600">{{ $t('page.admin.syncProgress.estimatedCompletion') }}:</span>
            <span class="ml-2">{{ new Date(syncProgress.estimated_completion).toLocaleString() }}</span>
          </div>
        </div>

        <!-- Loading indicator for running sync -->
        <div v-if="syncProgress.status === 'running' || syncProgress.status === 'pending'" class="flex items-center justify-center py-2">
          <NSpin size="small" />
          <span class="ml-2 text-sm text-gray-600">
            {{ syncProgress.total_records === 0 ? $t('page.admin.syncProgress.initializing') : $t('page.admin.syncProgress.syncInProgress') }}
          </span>
        </div>
      </div>

      <!-- Loading state when no progress data -->
      <div v-else class="flex items-center justify-center py-8">
        <NSpin size="medium" />
        <span class="ml-3">{{ $t('page.admin.syncProgress.loadingProgress') }}</span>
      </div>

      <template #action>
        <div class="flex justify-end gap-2">
          <!-- Stop sync button - only show when sync is running -->
          <NButton
            v-if="syncProgress && (syncProgress.status === 'running' || syncProgress.status === 'pending')"
            type="error"
            ghost
            @click="handleStopSync"
          >
            {{ $t('page.admin.syncProgress.stopSync') }}
          </NButton>

          <!-- Close button for completed/failed sync -->
          <NButton
            v-if="syncProgress && (syncProgress.status === 'completed' || syncProgress.status === 'failed')"
            type="primary"
            @click="closeSyncProgressModal"
          >
            {{ $t('page.admin.syncProgress.close') }}
          </NButton>

          <!-- Run in background button for running sync -->
          <NButton
            v-else-if="syncProgress && (syncProgress.status === 'running' || syncProgress.status === 'pending')"
            @click="closeSyncProgressModal"
          >
            {{ $t('page.admin.syncProgress.runInBackground') }}
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- Sync configuration modal -->
    <NModal
      v-model:show="showSyncConfigModal"
      preset="dialog"
      :title="$t('page.admin.syncConfig.title')"
      :style="isMobile ? 'width: calc(100vw - 32px); max-width: 100%;' : 'width: 500px;'"
    >
      <div class="space-y-4">
        <!-- Sync Type Selection -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ $t('page.admin.syncConfig.syncType') }}
          </label>
          <NSelect
            v-model:value="syncConfigForm.sync_type"
            :placeholder="$t('page.admin.syncConfig.selectSyncType')"
            :options="[
              { label: $t('page.admin.syncConfig.syncTypeOptions.product_sync'), value: 'product_sync' },
              { label: $t('page.admin.syncConfig.syncTypeOptions.order_sync'), value: 'order_sync' }
            ]"
          />
        </div>

        <!-- Sync Mode Selection -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ $t('page.admin.syncConfig.syncMode') }}
          </label>
          <NRadioGroup v-model:value="syncConfigForm.incremental">
            <div class="space-y-2">
              <NRadio :value="false">
                <div class="flex flex-col">
                  <span class="font-medium">{{ $t('page.admin.syncConfig.fullSync') }}</span>
                  <span class="text-xs text-gray-500">{{ $t('page.admin.syncConfig.fullSyncHelp') }}</span>
                </div>
              </NRadio>
              <NRadio :value="true">
                <div class="flex flex-col">
                  <span class="font-medium">{{ $t('page.admin.syncConfig.incrementalSync') }}</span>
                  <span class="text-xs text-gray-500">{{ $t('page.admin.syncConfig.incrementalSyncHelp') }}</span>
                </div>
              </NRadio>
            </div>
          </NRadioGroup>
        </div>

        <!-- Batch Size Configuration -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ $t('page.admin.syncConfig.batchSize') }}
          </label>
          <NInputNumber
            v-model:value="syncConfigForm.batch_size"
            :min="1"
            :max="1000"
            :step="10"
            class="w-full"
          />
          <div class="text-xs text-gray-500">
            {{ $t('page.admin.syncConfig.batchSizeHelp') }}
          </div>
        </div>

        <!-- Timeout Configuration -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ $t('page.admin.syncConfig.timeout') }} ({{ $t('page.admin.syncConfig.timeoutUnit') }})
          </label>
          <NInputNumber
            v-model:value="syncConfigForm.timeout"
            :min="60"
            :max="3600"
            :step="60"
            class="w-full"
          />
          <div class="text-xs text-gray-500">
            {{ $t('page.admin.syncConfig.timeoutHelp') }}
          </div>
        </div>
      </div>

      <template #action>
        <div class="flex justify-end gap-2">
          <NButton @click="handleCloseSyncConfig">
            {{ $t('page.admin.syncConfig.cancel') }}
          </NButton>
          <NButton
            type="primary"
            :loading="syncConfigLoading"
            @click="handleTriggerSyncWithConfig"
          >
            {{ $t('page.admin.syncConfig.confirm') }}
          </NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
/* Mobile and PC responsive styles */
@media (max-width: 768px) {
  /* Mobile-specific styles */
  .mobile-table .n-data-table {
    font-size: 12px;
  }

  .mobile-table .n-data-table th,
  .mobile-table .n-data-table td {
    padding: 6px 4px;
    min-width: 60px;
  }

  /* Make table horizontally scrollable on mobile */
  .mobile-table .n-data-table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Compact table headers on mobile */
  .mobile-table .n-data-table th {
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
  }

  /* Better mobile table cell content */
  .mobile-table .n-data-table td {
    font-size: 12px;
    line-height: 1.3;
  }

  /* Mobile table actions column */
  .mobile-table .n-data-table td:last-child {
    position: sticky;
    right: 0;
    background: var(--n-td-color);
    border-left: 1px solid var(--n-border-color);
    z-index: 1;
  }

  /* Adjust modal content for mobile */
  .n-descriptions-item-label {
    font-size: 13px;
    font-weight: 500;
  }

  .n-descriptions-item-content {
    font-size: 13px;
  }

  /* Compact spacing for mobile */
  .space-y-4 > * + * {
    margin-top: 12px;
  }

  /* Adjust button sizes for mobile */
  .n-button--small {
    padding: 4px 8px;
    font-size: 12px;
  }

  .n-button--tiny {
    padding: 2px 6px;
    font-size: 11px;
    min-height: 24px;
  }

  /* Make statistics cards more compact on mobile */
  .grid-cols-2 {
    gap: 8px;
  }

  .grid-cols-2 .border {
    padding: 12px;
  }

  .grid-cols-2 .text-2xl {
    font-size: 1.5rem;
  }

  /* Mobile search form optimizations */
  .space-y-3 .n-input,
  .space-y-3 .n-select {
    font-size: 14px;
  }

  .space-y-3 .text-sm {
    font-size: 12px;
    margin-bottom: 4px;
  }

  /* Mobile modal optimizations */
  .n-modal .n-dialog {
    margin: 8px;
    max-height: calc(100vh - 32px);
    overflow-y: auto;
  }

  /* Mobile pagination */
  .n-pagination {
    justify-content: center;
    flex-wrap: wrap;
  }

  .n-pagination .n-pagination-item {
    margin: 2px;
  }
}

@media (min-width: 769px) {
  /* PC-specific styles */
  .n-data-table {
    font-size: 14px;
  }

  .n-data-table th,
  .n-data-table td {
    padding: 12px 16px;
  }

  /* Better spacing for PC */
  .space-y-4 > * + * {
    margin-top: 16px;
  }

  /* Hover effects for PC */
  .n-data-table tbody tr:hover {
    background-color: var(--n-td-color-hover);
  }

  /* Better button spacing for PC */
  .flex.gap-2 {
    gap: 8px;
  }
}

/* Common styles for both mobile and PC */
.n-modal .n-dialog {
  margin: 16px;
}

/* Sync icon animation */
.animate-spin {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Ensure proper text wrapping in table cells */
.n-data-table td {
  word-break: break-word;
  white-space: normal;
}

/* Status tag styling */
.n-tag {
  font-weight: 500;
  text-transform: capitalize;
}

/* Loading state styling */
.n-data-table--loading {
  min-height: 200px;
}

/* Scrollable area for failed records */
.max-h-200px {
  max-height: 200px;
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 480px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>
