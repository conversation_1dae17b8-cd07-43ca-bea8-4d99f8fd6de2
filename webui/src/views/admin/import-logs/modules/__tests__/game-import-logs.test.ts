import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createI18n } from 'vue-i18n';
import GameImportLogs from '../game-import-logs.vue';

// Mock the API functions
vi.mock('@/service/api/sync', () => ({
  fetchSyncLogs: vi.fn(),
  fetchActiveSyncJobs: vi.fn(),
  fetchSyncLogDetail: vi.fn(),
  fetchRetrySync: vi.fn(),
  fetchTriggerSync: vi.fn(),
  fetchSyncProgress: vi.fn()
}));

// Mock the store
vi.mock('@/store/modules/app', () => ({
  useAppStore: () => ({
    isMobile: false
  })
}));

// Mock the locales
vi.mock('@/locales', () => ({
  $t: (key: string) => key
}));

// Create i18n instance for testing
const i18n = createI18n({
  locale: 'zh-CN',
  messages: {
    'zh-CN': {
      page: {
        admin: {
          dataSyncLogs: '数据同步日志',
          viewMode: '查看模式',
          logsMode: '日志模式',
          activeJobsMode: '正在执行',
          activeJobs: '正在执行的任务',
          noActiveJobs: '当前没有正在执行的任务',
          noActiveJobsDesc: '所有同步任务都已完成或暂停',
          autoRefresh: '自动刷新',
          refreshInterval: '刷新间隔',
          seconds: '秒',
          jobId: '任务ID',
          refresh: '刷新',
          triggerSync: '触发同步'
        }
      }
    }
  }
});

describe('GameImportLogs', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(GameImportLogs, {
      global: {
        plugins: [i18n],
        stubs: {
          NButton: true,
          NButtonGroup: true,
          NSelect: true,
          NDataTable: true,
          NModal: true,
          NDescriptions: true,
          NDescriptionsItem: true,
          NTag: true,
          NProgress: true,
          NSpin: true,
          SvgIcon: true,
          ButtonIcon: true
        }
      }
    });
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should have default view mode as logs', () => {
    expect(wrapper.vm.viewMode).toBe('logs');
  });

  it('should switch view mode correctly', async () => {
    await wrapper.vm.switchViewMode('active');
    expect(wrapper.vm.viewMode).toBe('active');

    await wrapper.vm.switchViewMode('logs');
    expect(wrapper.vm.viewMode).toBe('logs');
  });

  it('should toggle auto refresh correctly', () => {
    const initialState = wrapper.vm.autoRefreshEnabled;
    wrapper.vm.toggleAutoRefresh();
    expect(wrapper.vm.autoRefreshEnabled).toBe(!initialState);
  });

  it('should have correct refresh interval options', () => {
    expect(wrapper.vm.refreshInterval).toBe(5); // default value
  });

  it('should display mode selector buttons', () => {
    const text = wrapper.text();
    expect(text).toContain('page.admin.logsMode');
    expect(text).toContain('page.admin.activeJobsMode');
  });
});
