<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, inject, watch, type Ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { usePermission } from '@/hooks/common/permission';

import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import OrganizationSelector from '@/components/common/organization-selector.vue';

defineOptions({
  name: 'HeaderBanner'
});

const appStore = useAppStore();
const authStore = useAuthStore();
const { isSystemAdmin, userOrganizations } = usePermission();

// Ref to OrganizationSelector component
const organizationSelectorRef = ref();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// Get selected organization ID from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId');

// Computed property to get organization IDs for the selector based on user role
const allowedOrganizationIds = computed(() => {
  // If current user is root or admin, allow all organizations (no restriction)
  if (isSystemAdmin()) {
    console.log('User is system admin, allowing all organizations');
    return undefined;
  }

  // For non-admin users, return their organization IDs
  const orgIds = userOrganizations.value.map(org => org.id);
  console.log('User organizations for selector:', userOrganizations.value, 'IDs:', orgIds);

  // If user has no organizations, return empty array to prevent loading all organizations
  if (orgIds.length === 0) {
    console.log('User has no organizations, returning empty array');
    return [];
  }

  return orgIds;
});

// Handle organization selection change
function handleOrganizationChange(value: number | number[] | null) {
  console.log('Organization selection changed:', value);
  // Since we're not using multiple selection, value should be a single number or null
  if (Array.isArray(value)) {
    if (selectedOrganizationId) {
      selectedOrganizationId.value = value.length > 0 ? value[0] : null;
      console.log('Set selectedOrganizationId to:', selectedOrganizationId.value);
    }
  } else {
    if (selectedOrganizationId) {
      selectedOrganizationId.value = value;
      console.log('Set selectedOrganizationId to:', selectedOrganizationId.value);
    }
  }
}

// Handle when OrganizationSelector has loaded its options
function handleOrganizationSelectorReady(organizations: any[]) {
  console.log('OrganizationSelector ready with organizations:', organizations);

  // If user is system admin and no organization is selected, select the first one
  if (isSystemAdmin() && !selectedOrganizationId?.value && organizations.length > 0) {
    const firstOrg = organizations[0];
    if (selectedOrganizationId && firstOrg.value) {
      selectedOrganizationId.value = firstOrg.value;
      console.log('Auto-selected first organization for system admin:', firstOrg.value, firstOrg.label);
    }
  }
}

// Watch selectedOrganizationId changes
watch(
  () => selectedOrganizationId?.value,
  (newValue, oldValue) => {
    console.log('HeaderBanner: selectedOrganizationId changed from', oldValue, 'to', newValue);
  },
  { immediate: true }
);

// Watch for allowedOrganizationIds changes
watch(
  () => allowedOrganizationIds.value,
  (newIds) => {
    console.log('allowedOrganizationIds changed:', newIds);
  },
  { immediate: true }
);

// 当前时间显示
const currentTime = ref('');

// 获取时间问候语
const timeGreeting = computed(() => {
  const hour = new Date().getHours();
  const userName = authStore.userInfo.userName;

  if (hour < 12) {
    return $t('page.home.greeting.morning' as any, { userName });
  } else if (hour < 18) {
    return $t('page.home.greeting.afternoon' as any, { userName });
  }
  return $t('page.home.greeting.evening' as any, { userName });
});

// 更新时间
const updateTime = () => {
  const now = new Date();
  currentTime.value = `${now.toLocaleDateString()} ${now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
};

// 定时器
let timeInterval: NodeJS.Timeout;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

// 用户角色信息 - 按组织分组显示
const userRoles = computed(() => {
  const systemRoles = authStore.userSystemRoles;
  const organisationRoles = authStore.userOrganisationRoles;
  const organisations = authStore.userOrganisations;

  const roles = [];

  // 添加系统角色
  if (systemRoles.length > 0) {
    roles.push({
      type: 'system',
      id: 'system-roles', // 添加唯一ID用于key
      label: $t('page.user.systemRoles' as any),
      roles: systemRoles.map(role => role.name),
      color: 'error' as const, // 系统角色用红色
      icon: 'ph:shield-check',
      isPending: false,
      status: 'active'
    });
  }

  // 显示所有用户所属的组织（包括pending状态）
  if (organisations.length > 0) {
    // 按组织分组角色
    const rolesByOrg = new Map<number, any[]>();
    organisationRoles.forEach(role => {
      const orgId = role.organisation_id;
      if (orgId !== null) {
        if (!rolesByOrg.has(orgId)) {
          rolesByOrg.set(orgId, []);
        }
        rolesByOrg.get(orgId)?.push(role);
      }
    });

    // 为每个组织创建角色组（包括没有角色的组织）
    organisations.forEach(org => {
      const orgRoles = rolesByOrg.get(org.id) || [];
      const isPending = org.status === 'pending';

      // 确定显示的角色
      let displayRoles: string[] = [];
      let roleColor: 'primary' | 'warning' | 'error' | 'success' | 'info' = 'primary';

      if (orgRoles.length > 0) {
        // 有角色的情况
        displayRoles = orgRoles.map((role: any) => role.name);
        roleColor = isPending ? 'warning' : 'primary';
      } else if (isPending) {
        // pending状态但没有角色，不显示角色标签，只显示组织名和pending标识
        displayRoles = [];
        roleColor = 'warning';
      } else {
        // 其他状态且没有角色，不显示
        return;
      }

      roles.push({
        type: 'organisation',
        id: `org-${org.id}`,
        label: org.name,
        roles: displayRoles,
        color: roleColor,
        icon: 'ph:buildings',
        isPending,
        status: org.status
      });
    });
  }

  return roles;
});
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div :class="appStore.isMobile ? 'flex flex-col gap-4' : 'flex items-center justify-between'">
      <!-- 左侧用户信息 -->
      <div class="flex items-center">
        <div class="size-72px shrink-0 overflow-hidden rd-1/2">
          <img src="@/assets/imgs/soybean.jpg" class="size-full" />
        </div>
        <div class="pl-12px">
          <h3 class="text-18px font-semibold">
            {{ timeGreeting }}
          </h3>
          <p class="text-#999 leading-30px">{{ currentTime }}</p>
        </div>
      </div>

      <!-- 右侧角色信息和机构选择器 -->
      <div :class="appStore.isMobile ? 'pl-84px' : 'flex flex-col gap-2'">
        <!-- 角色信息 - 单行显示 -->
        <div v-if="userRoles.length > 0" :class="appStore.isMobile ? 'mb-2' : 'flex items-center justify-end gap-3'">
          <div
            v-for="(roleGroup, index) in userRoles"
            :key="roleGroup.id"
            class="flex items-center gap-1"
            :class="[appStore.isMobile ? 'mb-1' : '']"
          >
            <SvgIcon :icon="roleGroup.icon" class="text-12px text-gray-500" />
            <span class="text-sm text-gray-500">{{ roleGroup.label }}:</span>
            <!-- pending状态标识 -->
            <NTag v-if="roleGroup.isPending" type="warning" size="tiny" class="mr-1">
              {{ $t('common.pending') }}
            </NTag>
            <!-- 角色标签 -->
            <div v-if="roleGroup.roles.length > 0" class="flex items-center gap-1">
              <NTag v-for="roleName in roleGroup.roles" :key="roleName" :type="roleGroup.color" size="tiny">
                {{ roleName }}
              </NTag>
            </div>
            <!-- 分隔符 -->
            <span v-if="index < userRoles.length - 1" class="text-gray-300 mx-1">|</span>
          </div>
        </div>

        <!-- 机构选择器 -->
        <div :class="appStore.isMobile ? '' : 'flex items-center justify-end gap-2'">
          <SvgIcon icon="mdi:office-building" class="text-16px text-gray-500" />
          <div :class="appStore.isMobile ? 'w-full mt-1' : 'w-180px'">
            <OrganizationSelector
              ref="organizationSelectorRef"
              v-model="selectedOrganizationId"
              :organization-ids="allowedOrganizationIds"
              :placeholder="$t('common.selectOrganization')"
              size="small"
              clearable
              @change="handleOrganizationChange"
            />
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped></style>
