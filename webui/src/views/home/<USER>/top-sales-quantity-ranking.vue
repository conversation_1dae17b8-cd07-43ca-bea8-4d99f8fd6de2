<script setup lang="ts">
import { ref, onMounted, inject, watch, type Ref } from 'vue';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { fetchCurrentMonthVolumeByCountry } from '@/service/api/reports';

defineOptions({
  name: 'TopSalesQuantityRanking'
});

interface GameRankingData {
  id: string;
  name: string;
  code: string;
  coverImage: string;
  dailySalesQuantity: number;
  price: number;
  sales: number;
  enabled: boolean;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
}

interface Emits {
  (e: 'game-click', game: GameRankingData): void;
}

const emit = defineEmits<Emits>();

// Get selected organization ID from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId');

const loading = ref(true);
const totalQuantity = ref(0);

// 模拟TOP10游戏数据（按销量排序）
const rankingData = ref<GameRankingData[]>([
  {
    id: '3',
    name: 'Grand Theft Auto V',
    code: 'GTAV',
    coverImage: 'https://picsum.photos/200/300?random=3',
    dailySalesQuantity: 2911,
    price: 29.99,
    sales: 2911,
    enabled: true,
    multiLangName: { 'zh-CN': '侠盗猎车手5', 'en-US': 'Grand Theft Auto V' },
    languages: ['en', 'zh', 'ja', 'es'],
    onHand: 4500
  },
  {
    id: '2',
    name: 'The Witcher 3',
    code: 'TW3',
    coverImage: 'https://picsum.photos/200/300?random=2',
    dailySalesQuantity: 2461,
    price: 39.99,
    sales: 2461,
    enabled: true,
    multiLangName: { 'zh-CN': '巫师3', 'en-US': 'The Witcher 3' },
    languages: ['en', 'zh', 'ja', 'fr'],
    onHand: 3200
  },
  {
    id: '1',
    name: 'Cyberpunk 2077',
    code: 'CP2077',
    coverImage: 'https://picsum.photos/200/300?random=1',
    dailySalesQuantity: 2095,
    price: 59.99,
    sales: 2095,
    enabled: true,
    multiLangName: { 'zh-CN': '赛博朋克2077', 'en-US': 'Cyberpunk 2077' },
    languages: ['en', 'zh', 'ja'],
    onHand: 5000
  },
  {
    id: '7',
    name: 'Minecraft',
    code: 'MC',
    coverImage: 'https://picsum.photos/200/300?random=7',
    dailySalesQuantity: 1616,
    price: 26.95,
    sales: 1616,
    enabled: true,
    multiLangName: { 'zh-CN': '我的世界', 'en-US': 'Minecraft' },
    languages: ['en', 'zh', 'ja', 'fr', 'de', 'es'],
    onHand: 8000
  },
  {
    id: '4',
    name: 'Red Dead Redemption 2',
    code: 'RDR2',
    coverImage: 'https://picsum.photos/200/300?random=4',
    dailySalesQuantity: 1538,
    price: 49.99,
    sales: 1538,
    enabled: true,
    multiLangName: { 'zh-CN': '荒野大镖客2', 'en-US': 'Red Dead Redemption 2' },
    languages: ['en', 'zh', 'ja'],
    onHand: 2800
  },
  {
    id: '5',
    name: 'Elden Ring',
    code: 'ER',
    coverImage: 'https://picsum.photos/200/300?random=5',
    dailySalesQuantity: 1091,
    price: 59.99,
    sales: 1091,
    enabled: true,
    multiLangName: { 'zh-CN': '艾尔登法环', 'en-US': 'Elden Ring' },
    languages: ['en', 'zh', 'ja'],
    onHand: 3600
  },
  {
    id: '6',
    name: 'Call of Duty: Modern Warfare',
    code: 'CODMW',
    coverImage: 'https://picsum.photos/200/300?random=6',
    dailySalesQuantity: 783,
    price: 69.99,
    sales: 783,
    enabled: true,
    multiLangName: { 'zh-CN': '使命召唤：现代战争', 'en-US': 'Call of Duty: Modern Warfare' },
    languages: ['en', 'zh', 'ja', 'fr', 'de'],
    onHand: 2100
  },
  {
    id: '8',
    name: 'FIFA 24',
    code: 'FIFA24',
    coverImage: 'https://picsum.photos/200/300?random=8',
    dailySalesQuantity: 556,
    price: 69.99,
    sales: 556,
    enabled: true,
    multiLangName: { 'zh-CN': 'FIFA 24', 'en-US': 'FIFA 24' },
    languages: ['en', 'zh', 'ja', 'fr', 'de', 'es', 'pt'],
    onHand: 1500
  },
  {
    id: '9',
    name: 'Assassins Creed Valhalla',
    code: 'ACV',
    coverImage: 'https://picsum.photos/200/300?random=9',
    dailySalesQuantity: 536,
    price: 59.99,
    sales: 536,
    enabled: true,
    multiLangName: { 'zh-CN': '刺客信条：英灵殿', 'en-US': 'Assassins Creed Valhalla' },
    languages: ['en', 'zh', 'ja', 'fr'],
    onHand: 1800
  },
  {
    id: '10',
    name: 'Hogwarts Legacy',
    code: 'HL',
    coverImage: 'https://picsum.photos/200/300?random=10',
    dailySalesQuantity: 479,
    price: 59.99,
    sales: 479,
    enabled: true,
    multiLangName: { 'zh-CN': '霍格沃茨之遗', 'en-US': 'Hogwarts Legacy' },
    languages: ['en', 'zh', 'ja'],
    onHand: 2200
  }
]);

// 获取排名图标
function getRankIcon(rank: number) {
  switch (rank) {
    case 1:
      return 'mdi:trophy';
    case 2:
      return 'mdi:medal';
    case 3:
      return 'mdi:medal-outline';
    default:
      return 'mdi:numeric-' + rank + '-circle';
  }
}

// 获取排名颜色
function getRankColor(rank: number) {
  switch (rank) {
    case 1:
      return '#FFD700'; // 金色
    case 2:
      return '#C0C0C0'; // 银色
    case 3:
      return '#CD7F32'; // 铜色
    default:
      return '#666666'; // 灰色
  }
}

// Load volume data and update game rankings
async function loadVolumeData() {
  // Don't load data if no organization is selected
  if (!selectedOrganizationId?.value) {
    loading.value = false;
    return;
  }

  try {
    loading.value = true;

    const { data: response, error } = await fetchCurrentMonthVolumeByCountry(selectedOrganizationId.value);

    if (!error && response?.data) {
      const summary = response.data.summary;
      // Check if summary exists before accessing its properties
      if (summary) {
        totalQuantity.value = Math.round(summary.total_quantity || 0);
      } else {
        console.warn('No summary data available for quantity ranking');
        totalQuantity.value = 0;
      }

      // Update game rankings with proportional quantities based on total
      const baseQuantity = totalQuantity.value;
      if (baseQuantity > 0) {
        // Generate realistic distribution for top 10 games (different from sales amount)
        const distribution = [0.20, 0.16, 0.14, 0.12, 0.10, 0.08, 0.07, 0.06, 0.04, 0.03];

        rankingData.value.forEach((game, index) => {
          game.dailySalesQuantity = Math.round(baseQuantity * distribution[index]);
          game.sales = game.dailySalesQuantity; // For quantity ranking, sales equals daily quantity
        });
      }
    }
  } catch (error) {
    console.error('Failed to load volume data:', error);
  } finally {
    loading.value = false;
  }
}

// 处理游戏点击
function handleGameClick(game: GameRankingData) {
  emit('game-click', game);
}

// Load data when component mounts or organization IDs change
onMounted(() => {
  loadVolumeData();
});

// Watch for selected organization changes and reload data
watch(() => selectedOrganizationId?.value, () => {
  loadVolumeData();
}, { immediate: false });
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center gap-2">
      <SvgIcon icon="mdi:chart-bar" class="text-20px text-primary" />
      <h3 class="text-16px font-semibold">{{ $t('page.home.topSalesQuantityRanking') }}</h3>
    </div>

    <div class="space-y-2">
      <!-- Loading skeleton -->
      <div v-if="loading" class="space-y-2">
        <div v-for="i in 10" :key="`skeleton-${i}`" class="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
          <NSkeleton circle :width="32" :height="32" />
          <NSkeleton :width="40" :height="48" />
          <div class="flex-1 space-y-2">
            <NSkeleton text :width="120" />
            <NSkeleton text :width="80" />
          </div>
          <div class="text-right space-y-2">
            <NSkeleton text :width="80" />
            <NSkeleton text :width="60" />
          </div>
          <NSkeleton circle :width="16" :height="16" />
        </div>
      </div>

      <!-- Actual data -->
      <div
        v-else
        v-for="(game, index) in rankingData"
        :key="game.id"
        class="flex items-center gap-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors cursor-pointer"
        @click="handleGameClick(game)"
      >
        <!-- 排名 -->
        <div class="flex items-center justify-center w-8 h-8">
          <SvgIcon
            :icon="getRankIcon(index + 1)"
            class="text-20px"
            :style="{ color: getRankColor(index + 1) }"
          />
        </div>

        <!-- 游戏封面 -->
        <img
          :src="game.coverImage"
          :alt="game.name"
          class="w-10 h-12 rounded object-cover shadow-sm"
        />

        <!-- 游戏信息 -->
        <div class="flex-1 min-w-0">
          <div class="font-medium text-gray-900 dark:text-white truncate hover:text-primary transition-colors">
            {{ game.name }}
          </div>
          <div class="text-sm text-gray-500 font-mono">{{ game.code }}</div>
        </div>

        <!-- 销量 -->
        <div class="text-right">
          <div class="font-semibold text-primary">{{ game.dailySalesQuantity.toLocaleString() }}</div>
          <div class="text-sm text-gray-500">${{ game.price }}</div>
        </div>

        <!-- 查看详情图标 -->
        <div class="flex items-center">
          <SvgIcon icon="mdi:chevron-right" class="text-16px text-gray-400" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
