<script setup lang="ts">
import { ref, watch, inject, onMounted, type Ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import { fetchCurrentMonthVolumeByCountry } from '@/service/api/reports';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'RegionalSalesQuantityChart'
});

const appStore = useAppStore();

// Loading and error states
const loading = ref(true);
const error = ref<string | null>(null);

// Get selected organization ID from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId');

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: $t('page.home.salesQuantityByRegion'),
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    }
  },
  series: [
    {
      color: ['#ff7875', '#26deca', '#fedc69', '#8e9dff', '#5da8ff'],
      name: $t('page.home.salesQuantityByRegion'),
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{c}'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: true
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

async function loadChartData() {
  // Don't load data if no organization is selected
  if (!selectedOrganizationId?.value) {
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    error.value = null;

    const { data: response, error: apiError } = await fetchCurrentMonthVolumeByCountry(selectedOrganizationId.value);

    if (apiError || !response?.data) {
      const errorMessage = apiError?.message || 'Failed to load volume data';
      console.error('Failed to load volume data:', apiError);
      error.value = errorMessage;
      return;
    }

    const reportData = response.data.data;

    updateOptions(opts => {
      // Extract country data from the latest period
      const pieData: { name: string; value: number }[] = [];

      // Check if reportData exists and is an array
      if (reportData && Array.isArray(reportData) && reportData.length > 0) {
        const latestPeriod = reportData[reportData.length - 1];

        // Check if countries data exists
        if (latestPeriod?.countries && Array.isArray(latestPeriod.countries)) {
          latestPeriod.countries.forEach(country => {
            pieData.push({
              name: country.country_name,
              value: Math.round(country.quantity_sold || 0)
            });
          });
        }
      }

      // If no data, show placeholder
      if (pieData.length === 0) {
        pieData.push(
          { name: $t('common.noData'), value: 0 }
        );
      }

      opts.series[0].data = pieData;

      return opts;
    });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    console.error('Failed to load chart data:', err);
    error.value = errorMessage;

    // Fallback to empty data on error
    updateOptions(opts => {
      opts.series[0].data = [
        { name: $t('page.sales.noData'), value: 0 }
      ];
      return opts;
    });
  } finally {
    loading.value = false;
  }
}

function updateLocale() {
  try {
    updateOptions((opts, factory) => {
      const originOpts = factory();

      opts.title.text = originOpts.title.text;
      opts.series[0].name = originOpts.series[0].name;

      return opts;
    });

    // Reload data to update country names in current locale
    loadChartData();
  } catch (error) {
    console.error('Failed to update chart locale:', error);
  }
}

async function init() {
  loadChartData();
}

// Watch for locale changes
watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// Watch for selected organization changes and reload data
watch(() => selectedOrganizationId?.value, () => {
  loadChartData();
}, { immediate: false });

// Initialize component
onMounted(() => {
  init();
});
</script>

<template>
  <div class="h-360px overflow-hidden relative">
    <!-- Loading state -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 bg-opacity-75">
      <div class="flex flex-col items-center gap-2">
        <NSpin size="medium" />
        <span class="text-sm text-gray-500">{{ $t('common.loading') }}</span>
      </div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 bg-opacity-75">
      <div class="flex flex-col items-center gap-2 text-center p-4">
        <SvgIcon icon="mdi:alert-circle-outline" class="text-32px text-error" />
        <span class="text-sm text-gray-500">{{ $t('common.loadError') }}</span>
        <NButton size="small" @click="loadChartData">{{ $t('common.retry') }}</NButton>
      </div>
    </div>

    <!-- Chart -->
    <div ref="domRef" class="h-full w-full"></div>
  </div>
</template>

<style scoped></style>
