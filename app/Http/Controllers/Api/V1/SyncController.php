<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Resources\Api\V1\SyncLogCollection;
use App\Http\Resources\Api\V1\SyncLogResource;
use App\Jobs\ProductSyncJob;
use App\Jobs\OrderSyncJob;
use App\Jobs\OrderSyncValidationJob;
use App\Models\SyncLog;
use App\Services\ProductSyncService;
use App\Contracts\OrderSyncServiceInterface;
use App\Services\SyncProgressService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * Sync Controller
 * 
 * Handles synchronization operations including viewing sync logs,
 * triggering manual syncs, and retrying failed syncs.
 * Only accessible by system administrators.
 */
final class SyncController extends ApiController
{
    public function __construct(
        private readonly ProductSyncService $productSyncService,
        private readonly OrderSyncServiceInterface $orderSyncService,
        private readonly SyncProgressService $progressService
    ) {
        // Set up automatic authorization for sync operations
        $this->authorizeResource(SyncLog::class, 'syncLog');
    }

    /**
     * Display a listing of sync logs.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min((int) $request->get('per_page', 15), 100);
        
        $logs = SyncLog::with(['records' => function($query) {
                // Only load failed records for overview
                $query->where('status', 'failed')->limit(5);
            }])
            ->when($request->sync_type, function($query, $type) {
                return $query->where('sync_type', $type);
            })
            ->when($request->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return $this->successResponse(
            new SyncLogCollection($logs),
            'sync.logs_retrieved'
        );
    }

    /**
     * Display the specified sync log details.
     */
    public function show(SyncLog $syncLog): JsonResponse
    {
        return $this->successResponse(
            new SyncLogResource($syncLog),
            'sync.log_details_retrieved'
        );
    }

    /**
     * Manually trigger a new synchronization.
     */
    public function trigger(Request $request): JsonResponse
    {
        // Check permission for triggering sync
        $this->checkPermission('trigger', SyncLog::class);

        // Check if there are any active sync jobs
        if ($this->progressService->hasActiveSyncJobs()) {
            return $this->errorResponse(
                'sync.sync_already_running',
                ['active_jobs' => $this->progressService->getActiveSyncJobs()],
                409
            );
        }

        $config = $request->validate([
            'sync_type' => 'required|string|in:product_sync,order_sync',
            'incremental' => 'boolean',
            'batch_size' => 'integer|min:1|max:1000',
            'timeout' => 'integer|min:60|max:3600',
        ]);

        try {
            // Generate batch ID and dispatch appropriate job
            $batchId = Str::uuid()->toString();

            if ($config['sync_type'] === 'order_sync') {
                OrderSyncJob::dispatch($config, $batchId);
            } else {
                ProductSyncJob::dispatch($config, $batchId);
            }

            return $this->successResponse([
                'batch_id' => $batchId,
                'sync_type' => $config['sync_type'],
                'status' => 'queued',
                'message' => __('sync.sync_queued'),
            ], 'sync.sync_queued');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.sync_trigger_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Retry a failed synchronization.
     */
    public function retry(SyncLog $syncLog): JsonResponse
    {
        // Check permission for retrying sync
        $this->checkPermission('retry', $syncLog);

        if ($syncLog->status !== 'failed') {
            return $this->errorResponse(
                'sync.retry_invalid_status',
                ['current_status' => $syncLog->status],
                422
            );
        }

        // Check if there are any active sync jobs
        if ($this->progressService->hasActiveSyncJobs()) {
            return $this->errorResponse(
                'sync.sync_already_running',
                ['active_jobs' => $this->progressService->getActiveSyncJobs()],
                409
            );
        }

        try {
            // Generate new batch ID and dispatch appropriate retry job
            $newBatchId = Str::uuid()->toString();
            $config = $syncLog->sync_config ?? [];

            if ($syncLog->sync_type === 'order_sync') {
                OrderSyncJob::dispatch(
                    $config,
                    $newBatchId,
                    true, // isRetry
                    $syncLog->batch_id // originalBatchId
                );
            } else {
                ProductSyncJob::dispatch(
                    $config,
                    $newBatchId,
                    true, // isRetry
                    $syncLog->batch_id // originalBatchId
                );
            }

            return $this->successResponse([
                'new_batch_id' => $newBatchId,
                'original_batch_id' => $syncLog->batch_id,
                'sync_type' => $syncLog->sync_type,
                'status' => 'queued',
                'message' => __('sync.retry_queued'),
            ], 'sync.retry_queued');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.retry_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Get sync progress by job ID.
     */
    public function progress(Request $request): JsonResponse
    {
        // Check permission for viewing sync progress
        $this->authorize('viewAny', SyncLog::class);

        $request->validate([
            'job_id' => 'required_without:batch_id|string',
            'batch_id' => 'required_without:job_id|string',
        ]);

        try {
            $progressData = null;

            if ($request->job_id) {
                $progressData = $this->progressService->getProgress($request->job_id);
            } elseif ($request->batch_id) {
                $progressData = $this->progressService->getProgressByBatchId($request->batch_id);
            }

            if (!$progressData) {
                return $this->errorResponse(
                    'sync.progress_not_found',
                    [],
                    404
                );
            }

            return $this->successResponse($progressData, 'sync.progress_retrieved');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.progress_retrieval_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Get all active sync jobs.
     */
    public function activeJobs(): JsonResponse
    {
        // Check permission for viewing sync jobs
        $this->authorize('viewAny', SyncLog::class);

        try {
            $activeJobs = $this->progressService->getActiveSyncJobs();

            return $this->successResponse([
                'active_jobs' => $activeJobs,
                'count' => count($activeJobs),
            ], 'sync.active_jobs_retrieved');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.active_jobs_retrieval_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Clean up a stuck sync job.
     */
    public function cleanupJob(Request $request): JsonResponse
    {
        // Check permission for cleaning up sync jobs (use trigger permission for cleanup)
        $this->checkPermission('trigger', SyncLog::class);

        $validated = $request->validate([
            'job_id' => 'required|string|uuid',
        ]);

        try {
            $jobId = $validated['job_id'];

            // Get job details before cleanup
            $jobDetails = $this->progressService->getJobDetails($jobId);

            if (!$jobDetails) {
                return $this->errorResponse(
                    'sync.job_not_found',
                    ['job_id' => $jobId],
                    404
                );
            }

            // Clean up the stuck job
            $cleaned = $this->progressService->cleanupStuckJob($jobId);

            if ($cleaned) {
                return $this->successResponse([
                    'job_id' => $jobId,
                    'batch_id' => $jobDetails['batch_id'] ?? null,
                    'original_status' => $jobDetails['status'] ?? null,
                    'message' => __('sync.job_cleaned_up'),
                ], 'sync.job_cleaned_up');
            } else {
                return $this->errorResponse(
                    'sync.job_cleanup_failed',
                    ['job_id' => $jobId],
                    500
                );
            }
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.job_cleanup_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Get details about a specific sync job.
     */
    public function jobDetails(Request $request): JsonResponse
    {
        // Check permission for viewing sync jobs
        $this->checkPermission('viewAny', SyncLog::class);

        $validated = $request->validate([
            'job_id' => 'required|string|uuid',
        ]);

        try {
            $jobId = $validated['job_id'];
            $jobDetails = $this->progressService->getJobDetails($jobId);

            if (!$jobDetails) {
                return $this->errorResponse(
                    'sync.job_not_found',
                    ['job_id' => $jobId],
                    404
                );
            }

            return $this->successResponse([
                'job_details' => $jobDetails,
            ], 'sync.job_details_retrieved');
        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.job_details_retrieval_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Re-validate a failed Order sync validation.
     */
    public function revalidate(SyncLog $syncLog): JsonResponse
    {
        // Check permission for re-validation
        $this->authorize('retry', $syncLog);

        // Validate that this is an order sync log
        if ($syncLog->sync_type !== 'order_sync') {
            return $this->errorResponse(
                'sync.invalid_sync_type',
                ['sync_type' => $syncLog->sync_type, 'expected' => 'order_sync'],
                400
            );
        }

        // Check if sync log has completed successfully
        if ($syncLog->status !== 'completed') {
            return $this->errorResponse(
                'sync.sync_not_completed',
                ['current_status' => $syncLog->status],
                400
            );
        }

        // Check if validation results exist and failed
        $validationResults = $syncLog->validation_results;
        if (!$validationResults) {
            return $this->errorResponse(
                'sync.no_validation_results',
                ['sync_log_id' => $syncLog->id],
                400
            );
        }

        $overallStatus = $validationResults['overall_status'] ?? null;
        if ($overallStatus === 'passed') {
            return $this->errorResponse(
                'sync.validation_already_passed',
                ['current_status' => $overallStatus],
                400
            );
        }

        if (!in_array($overallStatus, ['failed', 'error'])) {
            return $this->errorResponse(
                'sync.validation_not_eligible_for_retry',
                ['current_status' => $overallStatus],
                400
            );
        }

        // Check if there are any active validation jobs for this sync log
        if ($this->progressService->hasActiveValidationJob($syncLog->batch_id)) {
            return $this->errorResponse(
                'sync.validation_already_running',
                ['batch_id' => $syncLog->batch_id],
                409
            );
        }

        try {
            // Generate new job ID for tracking
            $jobId = Str::uuid()->toString();

            // Clear previous validation results
            $syncLog->update([
                'validation_results' => array_merge($validationResults, [
                    'overall_status' => 'pending',
                    'revalidation_started_at' => now(),
                    'revalidation_job_id' => $jobId,
                ])
            ]);

            // Dispatch validation job
            OrderSyncValidationJob::dispatch(
                $syncLog->id,
                $syncLog->batch_id,
                $jobId
            );

            return $this->successResponse([
                'sync_log_id' => $syncLog->id,
                'batch_id' => $syncLog->batch_id,
                'validation_job_id' => $jobId,
                'status' => 'queued',
                'message' => __('sync.revalidation_queued'),
            ], 'sync.revalidation_queued');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'sync.revalidation_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }

    /**
     * Batch re-validate multiple failed Order sync validations.
     */
    public function batchRevalidate(Request $request): JsonResponse
    {
        // Check permission for batch re-validation
        $this->authorize('viewAny', SyncLog::class);

        $validated = $request->validate([
            'sync_log_ids' => 'required|array|min:1|max:50',
            'sync_log_ids.*' => 'required|integer|min:1',
        ]);

        $syncLogIds = $validated['sync_log_ids'];
        $results = [
            'successful' => [],
            'failed' => [],
            'total_requested' => count($syncLogIds),
            'successful_count' => 0,
            'failed_count' => 0,
        ];

        foreach ($syncLogIds as $syncLogId) {
            try {
                $syncLog = SyncLog::find($syncLogId);

                if (!$syncLog) {
                    $results['failed'][] = [
                        'sync_log_id' => $syncLogId,
                        'reason' => 'not_found',
                        'details' => ['error' => 'Sync log not found'],
                    ];
                    $results['failed_count']++;
                    continue;
                }

                // Validate conditions for revalidation
                $validationResult = $this->validateSyncLogForRevalidation($syncLog);

                if ($validationResult['valid']) {
                    // Generate new job ID and dispatch validation
                    $jobId = Str::uuid()->toString();

                    // Update validation results
                    $validationResults = $syncLog->validation_results ?? [];
                    $syncLog->update([
                        'validation_results' => array_merge($validationResults, [
                            'overall_status' => 'pending',
                            'revalidation_started_at' => now(),
                            'revalidation_job_id' => $jobId,
                        ])
                    ]);

                    // Dispatch validation job
                    OrderSyncValidationJob::dispatch(
                        $syncLog->id,
                        $syncLog->batch_id,
                        $jobId
                    );

                    $results['successful'][] = [
                        'sync_log_id' => $syncLog->id,
                        'batch_id' => $syncLog->batch_id,
                        'validation_job_id' => $jobId,
                        'status' => 'queued',
                    ];
                    $results['successful_count']++;
                } else {
                    $results['failed'][] = [
                        'sync_log_id' => $syncLog->id,
                        'batch_id' => $syncLog->batch_id,
                        'reason' => $validationResult['reason'],
                        'details' => $validationResult['details'],
                    ];
                    $results['failed_count']++;
                }
            } catch (\Exception $e) {
                $results['failed'][] = [
                    'sync_log_id' => $syncLogId,
                    'reason' => 'exception',
                    'details' => ['error' => $e->getMessage()],
                ];
                $results['failed_count']++;
            }
        }

        $message = $results['successful_count'] > 0
            ? 'sync.batch_revalidation_completed'
            : 'sync.batch_revalidation_all_failed';

        return $this->successResponse($results, $message);
    }

    /**
     * Validate if a sync log is eligible for revalidation.
     */
    private function validateSyncLogForRevalidation(SyncLog $syncLog): array
    {
        // Check sync type
        if ($syncLog->sync_type !== 'order_sync') {
            return [
                'valid' => false,
                'reason' => 'invalid_sync_type',
                'details' => ['sync_type' => $syncLog->sync_type, 'expected' => 'order_sync']
            ];
        }

        // Check sync status
        if ($syncLog->status !== 'completed') {
            return [
                'valid' => false,
                'reason' => 'sync_not_completed',
                'details' => ['current_status' => $syncLog->status]
            ];
        }

        // Check validation results exist
        $validationResults = $syncLog->validation_results;
        if (!$validationResults) {
            return [
                'valid' => false,
                'reason' => 'no_validation_results',
                'details' => ['sync_log_id' => $syncLog->id]
            ];
        }

        // Check validation status
        $overallStatus = $validationResults['overall_status'] ?? null;
        if ($overallStatus === 'passed') {
            return [
                'valid' => false,
                'reason' => 'validation_already_passed',
                'details' => ['current_status' => $overallStatus]
            ];
        }

        if (!in_array($overallStatus, ['failed', 'error'])) {
            return [
                'valid' => false,
                'reason' => 'validation_not_eligible_for_retry',
                'details' => ['current_status' => $overallStatus]
            ];
        }

        // Check for active validation jobs
        if ($this->progressService->hasActiveValidationJob($syncLog->batch_id)) {
            return [
                'valid' => false,
                'reason' => 'validation_already_running',
                'details' => ['batch_id' => $syncLog->batch_id]
            ];
        }

        return ['valid' => true];
    }

    /**
     * Get all sync logs that are eligible for revalidation.
     */
    public function revalidationCandidates(Request $request): JsonResponse
    {
        // Check permission for viewing sync logs
        $this->authorize('viewAny', SyncLog::class);

        $request->validate([
            'per_page' => 'integer|min:1|max:100',
            'page' => 'integer|min:1',
        ]);

        $perPage = $request->input('per_page', 15);

        // Query for order sync logs that are completed and have failed/error validation
        $query = SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'completed')
            ->whereNotNull('validation_results')
            ->where(function ($q) {
                $q->whereJsonContains('validation_results->overall_status', 'failed')
                  ->orWhereJsonContains('validation_results->overall_status', 'error');
            })
            ->orderBy('completed_at', 'desc');

        $syncLogs = $query->paginate($perPage);

        // Filter out logs that have active validation jobs
        $filteredLogs = $syncLogs->getCollection()->filter(function ($syncLog) {
            return !$this->progressService->hasActiveValidationJob($syncLog->batch_id);
        });

        // Update the collection with filtered results
        $syncLogs->setCollection($filteredLogs);

        return $this->successResponse([
            'sync_logs' => SyncLogCollection::make($syncLogs),
            'meta' => [
                'current_page' => $syncLogs->currentPage(),
                'per_page' => $syncLogs->perPage(),
                'total' => $syncLogs->total(),
                'last_page' => $syncLogs->lastPage(),
                'from' => $syncLogs->firstItem(),
                'to' => $syncLogs->lastItem(),
            ],
            'summary' => [
                'total_candidates' => $filteredLogs->count(),
                'failed_validations' => $filteredLogs->where('validation_results.overall_status', 'failed')->count(),
                'error_validations' => $filteredLogs->where('validation_results.overall_status', 'error')->count(),
            ],
        ], 'sync.revalidation_candidates_retrieved');
    }
}
