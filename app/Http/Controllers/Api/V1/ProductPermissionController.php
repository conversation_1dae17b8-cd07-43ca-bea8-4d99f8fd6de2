<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\BulkProductPermissionRequest;
use App\Http\Requests\Api\V1\GrantProductAccessRequest;
use App\Http\Requests\Api\V1\RevokeProductAccessRequest;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use App\Services\ProductPermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Product Permission Controller
 * 
 * Handles API endpoints for managing product-level permissions.
 * Allows system administrators and organization owners to grant/revoke
 * product access permissions to users.
 */
final class ProductPermissionController extends ApiController
{
    public function __construct(
        private readonly ProductPermissionService $productPermissionService
    ) {
        // Set up automatic authorization for ProductPermission resource
        $this->authorizeResource(ProductPermission::class, 'productPermission');
    }

    /**
     * Grant product access permission to a user.
     * 
     * @param Product $product The product to grant access to
     * @param GrantProductAccessRequest $request The validated request
     * @return JsonResponse
     */
    public function grantAccess(Product $product, GrantProductAccessRequest $request): JsonResponse
    {
        $data = $request->getProcessedData();
        $user = User::findOrFail($data['user_id']);

        // Authorization check using policy
        $this->authorize('grantProductAccess', [ProductPermission::class, $product, $user]);

        $permission = $this->productPermissionService->grantProductAccess(
            user: $user,
            product: $product,
            permissionType: $data['permission_type'],
            grantedBy: $request->user(),
            expiresAt: $data['expires_at'] ?? null,
            notes: $data['notes'] ?? null
        );

        return $this->successResponse([
            'permission' => [
                'id' => $permission->id,
                'user_id' => $permission->user_id,
                'product_id' => $permission->product_id,
                'permission_type' => $permission->permission_type,
                'expires_at' => $permission->expires_at?->toISOString(),
                'granted_at' => $permission->granted_at->toISOString(),
                'granted_by' => $permission->granted_by,
                'notes' => $permission->notes,
            ]
        ], 'Product access granted successfully', 201);
    }

    /**
     * Revoke product access permission from a user.
     * 
     * @param Product $product The product to revoke access from
     * @param User $user The user to revoke access from
     * @param RevokeProductAccessRequest $request The validated request
     * @return JsonResponse
     */
    public function revokeAccess(Product $product, User $user, RevokeProductAccessRequest $request): JsonResponse
    {
        $data = $request->getProcessedData();

        // Authorization check using policy
        $this->authorize('revokeProductAccess', [ProductPermission::class, $product, $user]);

        $revoked = $this->productPermissionService->revokeProductAccess(
            $user,
            $product,
            $data['permission_type']
        );

        if ($revoked) {
            return $this->successResponse(
                null,
                'Product access revoked successfully'
            );
        }

        return $this->errorResponse(
            'No permission found to revoke',
            null,
            404
        );
    }

    /**
     * Get users authorized for a specific product.
     * 
     * @param Product $product The product to get authorized users for
     * @param Request $request The request
     * @return JsonResponse
     */
    public function getAuthorizedUsers(Product $product, Request $request): JsonResponse
    {
        // Authorization check using policy
        $this->authorize('viewProductPermissions', [ProductPermission::class, $product]);

        $permissionType = $request->get('permission_type', 'view-reports');
        $users = $this->productPermissionService->getProductAuthorizedUsers($product, $permissionType);

        return $this->successResponse([
            'users' => $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ];
            })
        ]);
    }

    /**
     * Get products accessible by the current user.
     * 
     * @param Request $request The request
     * @return JsonResponse
     */
    public function getUserAccessibleProducts(Request $request): JsonResponse
    {
        $user = $request->user();

        // Authorization check using policy
        $this->authorize('viewAccessibleProducts', ProductPermission::class);

        $products = $this->productPermissionService->getUserAccessibleProducts($user);

        return $this->successResponse([
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'organisation' => [
                        'id' => $product->organisation->id,
                        'name' => $product->organisation->name,
                        'code' => $product->organisation->code,
                    ]
                ];
            })
        ]);
    }

    /**
     * Get product permissions for a specific user.
     * 
     * @param User $user The user to get permissions for
     * @param Request $request The request
     * @return JsonResponse
     */
    public function getUserProductPermissions(User $user, Request $request): JsonResponse
    {
        // Authorization check using policy
        $this->authorize('viewUserProductPermissions', [ProductPermission::class, $user]);

        $organisationId = $request->get('organisation_id');
        
        if ($organisationId) {
            $permissions = $this->productPermissionService->getUserProductPermissionsInOrganisation(
                $user, 
                (int) $organisationId
            );
        } else {
            $permissions = $user->getValidProductPermissions();
        }

        return $this->successResponse([
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'product' => [
                        'id' => $permission->product->id,
                        'name' => $permission->product->name,
                        'slug' => $permission->product->slug,
                        'organisation' => [
                            'id' => $permission->product->organisation->id,
                            'name' => $permission->product->organisation->name,
                        ]
                    ],
                    'permission_type' => $permission->permission_type,
                    'expires_at' => $permission->expires_at?->toISOString(),
                    'granted_at' => $permission->granted_at->toISOString(),
                    'granted_by' => $permission->grantedBy ? [
                        'id' => $permission->grantedBy->id,
                        'name' => $permission->grantedBy->name,
                    ] : null,
                    'notes' => $permission->notes,
                ];
            })
        ]);
    }

    /**
     * Grant multiple product permissions to a user.
     * 
     * @param BulkProductPermissionRequest $request The validated request
     * @return JsonResponse
     */
    public function grantMultipleAccess(BulkProductPermissionRequest $request): JsonResponse
    {
        $data = $request->getProcessedData();
        $user = User::findOrFail($data['user_id']);

        // Authorization check using policy
        $this->authorize('grantMultipleProductAccess', [ProductPermission::class, $data['product_ids'], $user]);

        $permissions = $this->productPermissionService->grantMultipleProductAccess(
            user: $user,
            productIds: $data['product_ids'],
            permissionType: $data['permission_type'],
            grantedBy: $request->user(),
            expiresAt: $data['expires_at'] ?? null,
            notes: $data['notes'] ?? null
        );

        return $this->successResponse([
            'granted_count' => $permissions->count(),
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'product_id' => $permission->product_id,
                    'permission_type' => $permission->permission_type,
                    'expires_at' => $permission->expires_at?->toISOString(),
                ];
            })
        ], 'Multiple product access granted successfully', 201);
    }

    /**
     * Revoke multiple product permissions from a user.
     * 
     * @param User $user The user to revoke permissions from
     * @param Request $request The request
     * @return JsonResponse
     */
    public function revokeMultipleAccess(User $user, Request $request): JsonResponse
    {
        $request->validate([
            'product_ids' => 'required|array|min:1|max:100',
            'product_ids.*' => 'integer|exists:products,id',
            'permission_type' => 'sometimes|string|in:view-reports,edit-reports,export-reports'
        ]);

        $productIds = $request->input('product_ids');
        $permissionType = $request->input('permission_type', 'view-reports');

        // Authorization check using policy
        $this->authorize('revokeMultipleProductAccess', [ProductPermission::class, $productIds, $user]);

        $revokedCount = $this->productPermissionService->revokeMultipleProductAccess(
            $user,
            $productIds,
            $permissionType
        );

        return $this->successResponse([
            'revoked_count' => $revokedCount
        ], 'Multiple product access revoked successfully');
    }
}
