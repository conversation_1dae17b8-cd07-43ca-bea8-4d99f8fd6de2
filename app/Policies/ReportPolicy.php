<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;

/**
 * Report Policy
 *
 * Controls access to report functionality based on the existing four-tier permission system:
 * - Root/Admin: Can view all organization reports
 * - Organisation Owner: Can view reports for their organizations
 * - Organisation Member: Can view basic reports for their organizations
 *
 * Enhanced with product-level permissions:
 * - Product permissions take priority over organization permissions
 * - Users can access specific product reports even if not organization members
 * - Cross-organization data access is prevented through filtering
 *
 * Report data is filtered by organization and product permissions in the service layer.
 */
final class ReportPolicy
{
    /**
     * Determine whether the user can export reports for a specific organisation
     *
     * This method supports <PERSON><PERSON>'s authorize() pattern:
     * - exportForOrganisation($user, 'report', $organisationId) - <PERSON>vel's authorize() pattern
     */
    public function exportForOrganisation(User $user, ?string $model = null, ?int $organisationId = null): bool
    {
        $actualOrganisationId = is_numeric($model) ? (int) $model : $organisationId;

        return $user->canAccessReportsForOrganisation($actualOrganisationId);
    }

    /**
     * Determine whether the user can access reports for a specific product
     *
     * This method checks product-level permissions first, then falls back to organization permissions.
     * It allows users to access specific product reports even if they are not members of the
     * product's organization through product-level permissions.
     */
    public function viewForProduct(User $user, int $productId): bool
    {
        return $user->canAccessReportsForProduct($productId);
    }

    /**
     * Comprehensive check for organization and product permissions
     *
     * This method prioritizes product permissions to ensure precise permission control.
     * It allows access when users have product-level permissions within the specified organization.
     *
     * Priority logic:
     * 1. System administrators have full access
     * 2. Check if user belongs to the organization (basic organization access)
     * 3. Check if user has any accessible product IDs within the organization
     * 4. Product permissions take priority over organization permissions
     * 5. Cross-organization data access is prevented through filtering
     */
    public function viewForOrganisationOrProducts(User $user, ?int $organisationId): bool
    {
        // System administrators have full access to all reports
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Reject access if no organization ID is specified
        if (!$organisationId) {
            return false;
        }

        // First check if user belongs to the organization (basic organization access)
        // This allows access even if the organization has no products
        if ($user->belongsToOrganisation($organisationId)) {
            return true;
        }

        // If user doesn't belong to the organization, check for product-level permissions
        $userProductIds = $user->getAccessibleProductIds($organisationId);

        // Allow access if user has any accessible products within the organization
        // Specific product filtering is handled at the data layer
        return !empty($userProductIds);
    }
}
