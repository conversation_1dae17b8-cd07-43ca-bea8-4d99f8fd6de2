<?php

declare(strict_types=1);

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

final class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, HasRoles, Notifiable;

    /**
     * The guard name for roles and permissions.
     * Note: We use multiple guards - 'api' for organisation roles and 'system' for global admin roles.
     *
     * @var string
     */
    protected $guard_name = 'api';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the organisations that the user belongs to.
     */
    public function organisations(): BelongsToMany
    {
        return $this->belongsToMany(Organisation::class, 'user_organisation')
            ->withTimestamps();
    }

    /**
     * Get the team ID for permission scoping.
     * This method is used by Spatie Permission for team-based permissions.
     * For multi-organisation users, this returns the first organisation ID.
     */
    public function getPermissionTeamId(): ?int
    {
        return $this->organisations()->first()?->id;
    }

    /**
     * Check if user has system-wide admin access (Root or Admin with system guard).
     */
    public function hasSystemAdminAccess(): bool
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        return $this->roles()
            ->whereIn('roles.name', ['root', 'admin'])
            ->where('roles.guard_name', 'system')
            ->exists();
    }

    /**
     * Check if user has organisation-level admin access for a specific organisation.
     */
    public function hasOrganisationAdminAccess(int $organisationId): bool
    {
        // System admins have access to all organisations
        if ($this->hasSystemAdminAccess()) {
            return true;
        }

        // Set team context for the query to work with Spatie Permission
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);

        // Only check for owner role with api guard (root/admin should only exist with system guard)
        // The organisation_id condition already ensures the user belongs to the organisation
        return $this->roles()
            ->where('roles.name', 'owner')
            ->where('roles.guard_name', 'api')
            ->where('roles.organisation_id', $organisationId)
            ->exists();
    }

    /**
     * Check if user is a system root user.
     */
    public function isSystemRoot(): bool
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        return $this->roles()
            ->where('roles.name', 'root')
            ->where('roles.guard_name', 'system')
            ->exists();
    }

    /**
     * Check if user is a system admin user.
     */
    public function isSystemAdmin(): bool
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        return $this->roles()
            ->where('roles.name', 'admin')
            ->where('roles.guard_name', 'system')
            ->exists();
    }

    /**
     * Check if user belongs to a specific organisation.
     */
    public function belongsToOrganisation(int $organisationId): bool
    {
        return $this->organisations()->where('organisations.id', $organisationId)->exists();
    }

    /**
     * Check if a role belongs to an organisation that the user has access to.
     */
    public function canAccessRole(Role $role): bool
    {
        // System roles (no organisation) can be accessed by system admins
        if ($role->organisation_id === null) {
            return $this->hasSystemAdminAccess();
        }

        // Organisation roles can be accessed if user belongs to that organisation
        return $this->belongsToOrganisation($role->organisation_id);
    }

    /**
     * Check if user can assign a role to a target user.
     */
    public function canAssignRoleToUser(Role $role, User $targetUser): bool
    {
        // System roles can only be assigned by system admins
        if ($role->organisation_id === null) {
            return $this->hasSystemAdminAccess();
        }

        // Organisation roles require both users to belong to the organisation
        return $role->organisation_id !== null
            && $this->belongsToOrganisation($role->organisation_id)
            && $targetUser->belongsToOrganisation($role->organisation_id);
    }

    /**
     * Get all organisation IDs that the user belongs to.
     */
    public function getOrganisationIds(): \Illuminate\Support\Collection
    {
        return $this->organisations()->pluck('organisations.id');
    }

    /**
     * Check if user can access reports for a specific organisation.
     * This method encapsulates the core organization permission logic used by ReportPolicy.
     *
     * @param int|null $organisationId Organisation ID to check access for (null for system users)
     * @return bool True if user can access reports for the specified organisation
     */
    public function canAccessReportsForOrganisation(?int $organisationId): bool
    {
        // System admins can access any organisation's reports
        if ($this->hasSystemAdminAccess()) {
            return true;
        }

        // Non-system users must provide an organisation ID
        if ($organisationId === null) {
            return false;
        }

        // Check if user belongs to the specified organisation
        return $this->belongsToOrganisation($organisationId);
    }

    /**
     * Get all role names for the user across all contexts (system and organisation).
     */
    public function getAllRoleNames(): \Illuminate\Support\Collection
    {
        $allRoles = collect();

        // Get system-wide roles (clear team context first)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRoles = $this->roles()
            ->where('roles.guard_name', 'system')
            ->whereNull('roles.organisation_id')
            ->pluck('roles.name');
        $allRoles = $allRoles->merge($systemRoles);

        // Get organisation roles for all user's organisations
        $userOrganisationIds = $this->getOrganisationIds();

        foreach ($userOrganisationIds as $organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            $orgRoles = $this->roles()
                ->where('roles.guard_name', 'api')
                ->where('roles.organisation_id', $organisationId)
                ->pluck('roles.name');
            $allRoles = $allRoles->merge($orgRoles);
        }

        return $allRoles->unique();
    }

    /**
     * Check if user has owner role for a specific organisation.
     */
    public function hasOwnerRoleForOrganisation(int $organisationId): bool
    {
        // Set team context for the query
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);

        return $this->roles()
            ->where('roles.name', 'owner')
            ->where('roles.guard_name', 'api')
            ->where('roles.organisation_id', $organisationId)
            ->exists();
    }

    /**
     * Check if user has owner role in any organisation.
     */
    public function hasOwnerRoleInAnyOrganisation(): bool
    {
        return $this->getOwnedOrganisationIds()->isNotEmpty();
    }

    /**
     * Get organisation IDs where user has owner role using optimized query.
     */
    public function getOwnedOrganisationIds(): \Illuminate\Support\Collection
    {
        return \DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_type', self::class)
            ->where('model_has_roles.model_id', $this->id)
            ->where('roles.name', 'owner')
            ->where('roles.guard_name', 'api')
            ->whereNotNull('roles.organisation_id')
            ->pluck('roles.organisation_id');
    }

    /**
     * Get the product permissions for this user.
     */
    public function productPermissions(): HasMany
    {
        return $this->hasMany(ProductPermission::class);
    }

    /**
     * Get user's accessible product IDs (prioritizing product permissions over organisation permissions).
     * This method implements the core logic for product-level access control.
     */
    public function getAccessibleProductIds(?int $organisationId = null): array
    {
        // 0. System administrators have access to all products
        if ($this->hasSystemAdminAccess()) {
            if ($organisationId) {
                // If organisation ID is specified, return products for that organisation
                $organisationService = app(\App\Services\OrganisationService::class);
                return $organisationService->getOrganisationProductIds($organisationId);
            }
            // If no organisation specified, return all product store_variant_ids
            return DB::table('products')->pluck('store_variant_id')->toArray();
        }

        // 1. Priority check: product-level permissions
        $permissionProductIds = $this->getProductPermissionIds();

        // If user has product-level permissions, only return these product IDs
        // This ensures precise permission control: when admin grants product permissions,
        // they want to restrict user to only see these specific products' reports
        if (!empty($permissionProductIds)) {
            // If organisation ID is specified, filter products to ensure they belong to that organisation
            // This prevents cross-organisation data access security vulnerability
            if ($organisationId) {
                return $this->filterProductsByOrganisation($permissionProductIds, $organisationId);
            }
            return $permissionProductIds;
        }

        // 2. If no product-level permissions, use organisation-level permissions (existing logic)
        if ($organisationId && $this->belongsToOrganisation($organisationId)) {
            $organisationService = app(\App\Services\OrganisationService::class);
            return $organisationService->getOrganisationProductIds($organisationId);
        }

        // 3. Neither product permissions nor organisation membership, return empty array
        return [];
    }

    /**
     * Filter product IDs to ensure they belong to the specified organisation.
     * Prevents cross-organisation data access security vulnerability.
     */
    private function filterProductsByOrganisation(array $productIds, int $organisationId): array
    {
        if (empty($productIds)) {
            return [];
        }

        return DB::table('products')
            ->join('organisations', 'products.owner_id', '=', 'organisations.code')
            ->where('organisations.id', $organisationId)
            ->whereIn('products.id', $productIds)
            ->pluck('products.id')
            ->toArray();
    }

    /**
     * Get user's accessible product IDs through the dedicated permissions table.
     */
    public function getProductPermissionIds(): array
    {
        return $this->productPermissions()
            ->valid() // Only get valid (non-expired) permissions
            ->where('permission_type', 'view-reports')
            ->pluck('product_id')
            ->toArray();
    }

    /**
     * Check if user has permission for a specific product.
     */
    public function hasProductPermission(int $productId, string $permissionType = 'view-reports'): bool
    {
        return $this->productPermissions()
            ->where('product_id', $productId)
            ->where('permission_type', $permissionType)
            ->valid()
            ->exists();
    }

    /**
     * Check if user can access reports for a specific product.
     * Prioritizes system admin access, then product permissions, then organisation permissions.
     */
    public function canAccessReportsForProduct(int $productId): bool
    {
        // 1. System administrators have access to all products
        if ($this->hasSystemAdminAccess()) {
            return true;
        }

        // 2. Priority check: product-level permissions
        if ($this->hasProductPermission($productId, 'view-reports')) {
            return true;
        }

        // 3. If no product permission, check organisation permissions
        $product = Product::find($productId);
        if ($product && $product->organisation) {
            return $this->belongsToOrganisation($product->organisation->id);
        }

        return false;
    }

    /**
     * Get user's valid product permissions with details.
     */
    public function getValidProductPermissions(): Collection
    {
        return $this->productPermissions()
            ->with(['product', 'grantedBy'])
            ->valid()
            ->get();
    }
}
