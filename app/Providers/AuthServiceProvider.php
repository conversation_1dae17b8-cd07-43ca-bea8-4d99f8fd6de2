<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\ProductPermission;
use App\Models\Role;
use App\Models\SyncLog;
use App\Models\User;
use App\Policies\InvitationPolicy;
use App\Policies\OrganisationPolicy;
use App\Policies\ProductPermissionPolicy;
use App\Policies\ReportPolicy;
use App\Policies\RolePolicy;
use App\Policies\SyncPolicy;
use App\Policies\UserPolicy;
use App\Policies\UserRolePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

final class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => UserPolicy::class,
        Role::class => RolePolicy::class,
        Organisation::class => OrganisationPolicy::class,
        Invitation::class => InvitationPolicy::class,
        SyncLog::class => SyncPolicy::class,
        ProductPermission::class => ProductPermissionPolicy::class,
        'user_role' => UserRolePolicy::class,
        'report' => ReportPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}
