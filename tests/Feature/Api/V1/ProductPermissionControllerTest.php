<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

/**
 * Product Permission Controller Test
 * 
 * Tests API endpoints for product permission management.
 */
final class ProductPermissionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $rootUser;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $regularUser;
    private Organisation $organisation;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create system roles using PermissionService
        $permissionService = app(\App\Services\PermissionService::class);
        $systemRoles = $permissionService->createSystemRoles();
        $rootRole = collect($systemRoles)->firstWhere('name', 'root');
        $adminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation
        $this->organisation = Organisation::factory()->create();

        // Create organisation roles using PermissionService
        $orgRoles = $permissionService->createDefaultRoles($this->organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create product
        $this->product = Product::factory()->ownedBy($this->organisation->code)->create();

        // Create users with different roles
        $this->rootUser = User::factory()->create();
        $this->adminUser = User::factory()->create();
        $this->ownerUser = User::factory()->create();
        $this->memberUser = User::factory()->create();
        $this->regularUser = User::factory()->create();

        // Assign system roles (need to use system guard)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($rootRole);
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);

        // Associate users with organisation and assign roles
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($ownerRole);
        $this->memberUser->assignRole($memberRole);
    }

    public function test_grant_access_success_as_root_user(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports',
            'notes' => 'Test permission grant'
        ]);

        if ($response->status() !== 201) {
            dump($response->json());
        }

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'permission' => [
                        'id',
                        'user_id',
                        'product_id',
                        'permission_type',
                        'expires_at',
                        'granted_at',
                        'granted_by',
                        'notes'
                    ]
                ]
            ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $this->rootUser->id,
            'notes' => 'Test permission grant'
        ]);
    }

    public function test_grant_access_success_as_organization_owner(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $this->ownerUser->id
        ]);
    }

    public function test_grant_access_with_expiration_date(): void
    {
        Sanctum::actingAs($this->rootUser);

        $expiresAt = Carbon::now()->addDays(30)->toISOString();

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports',
            'expires_at' => $expiresAt
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);
    }

    public function test_grant_access_forbidden_for_member(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(403);
    }

    public function test_grant_access_validation_errors(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Missing user_id
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['user_id']);

        // Invalid permission type
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'invalid-permission'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permission_type']);

        // Invalid expiration date
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDays(1)->toISOString()
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['expires_at']);
    }

    public function test_revoke_access_success(): void
    {
        // Create a permission first
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        Sanctum::actingAs($this->rootUser);

        $response = $this->deleteJson("/api/v1/products/{$this->product->id}/permissions/{$this->regularUser->id}", [
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseMissing('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);
    }

    public function test_revoke_access_not_found(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->deleteJson("/api/v1/products/{$this->product->id}/permissions/{$this->regularUser->id}", [
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(404);
    }

    public function test_get_authorized_users_success(): void
    {
        // Create permissions for multiple users
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        ProductPermission::factory()->create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson("/api/v1/products/{$this->product->id}/authorized-users");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'users' => [
                        '*' => ['id', 'name', 'email']
                    ]
                ]
            ]);

        $users = $response->json('data.users');
        $this->assertCount(2, $users);
    }

    public function test_get_user_accessible_products_success(): void
    {
        // Create permission for the user
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/products/accessible');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'products' => [
                        '*' => [
                            'id',
                            'name',
                            'slug',
                            'organisation' => ['id', 'name', 'code']
                        ]
                    ]
                ]
            ]);
    }

    public function test_get_user_product_permissions_success(): void
    {
        // Create permission
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $this->ownerUser->id,
            'notes' => 'Test permission'
        ]);

        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson("/api/v1/users/{$this->regularUser->id}/product-permissions");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'permissions' => [
                        '*' => [
                            'id',
                            'product' => [
                                'id',
                                'name',
                                'slug',
                                'organisation' => ['id', 'name']
                            ],
                            'permission_type',
                            'expires_at',
                            'granted_at',
                            'granted_by',
                            'notes'
                        ]
                    ]
                ]
            ]);
    }

    public function test_grant_multiple_access_success(): void
    {
        $product2 = Product::factory()->ownedBy($this->organisation->code)->create();

        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson('/api/v1/product-permissions/grant-multiple', [
            'user_id' => $this->regularUser->id,
            'product_ids' => [$this->product->id, $product2->id],
            'permission_type' => 'view-reports',
            'notes' => 'Bulk permission grant'
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'granted_count',
                    'permissions'
                ]
            ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports'
        ]);
    }

    public function test_unauthenticated_access_denied(): void
    {
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(401);
    }
}
