<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class UserTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;

    // System roles
    private Role $systemRootRole;
    private Role $systemAdminRole;

    // Organisation roles
    private Role $ownerRole;
    private Role $memberRole;

    // Test users
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->systemRootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $this->ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create test users and assign roles
        $this->createTestUsers();
    }

    private function createTestUsers(): void
    {
        // Create system users (no organisation)
        $this->systemRootUser = User::factory()->create();
        $this->systemAdminUser = User::factory()->create();

        // Create organisation users
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($this->systemRootRole);

        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);
    }

    // ========================================
    // System-level Role Tests
    // ========================================

    public function test_system_root_has_system_admin_access(): void
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        $this->assertTrue($this->systemRootUser->hasSystemAdminAccess());
        $this->assertTrue($this->systemRootUser->isSystemRoot());
        $this->assertFalse($this->systemRootUser->isSystemAdmin());
    }

    public function test_system_admin_has_system_admin_access(): void
    {
        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        $this->assertTrue($this->systemAdminUser->hasSystemAdminAccess());
        $this->assertFalse($this->systemAdminUser->isSystemRoot());
        $this->assertTrue($this->systemAdminUser->isSystemAdmin());
    }

    // ========================================
    // Organisation-level Role Tests
    // ========================================

    public function test_organisation_member_does_not_have_system_admin_access(): void
    {
        $this->assertFalse($this->memberUser->hasSystemAdminAccess());
        $this->assertFalse($this->memberUser->isSystemRoot());
        $this->assertFalse($this->memberUser->isSystemAdmin());
    }

    public function test_organisation_owner_has_organisation_admin_access(): void
    {
        $this->assertTrue($this->ownerUser->hasOrganisationAdminAccess($this->organisation->id));
    }

    public function test_organisation_member_does_not_have_organisation_admin_access(): void
    {
        $this->assertFalse($this->memberUser->hasOrganisationAdminAccess($this->organisation->id));
    }

    public function test_organisation_owner_has_access_after_context_reset(): void
    {
        // Intentionally set wrong team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(999);

        // Should still be able to check permissions correctly (method will set correct context internally)
        $this->assertTrue($this->ownerUser->hasOrganisationAdminAccess($this->organisation->id));
    }

    public function test_organisation_member_does_not_have_access_after_context_reset(): void
    {
        // Intentionally set wrong team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(999);

        // Member user should still not have admin permissions
        $this->assertFalse($this->memberUser->hasOrganisationAdminAccess($this->organisation->id));
    }

    public function test_system_admin_access_not_affected_by_team_context(): void
    {
        // Set organisation team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);

        // System admin should not be affected by team context
        $this->assertTrue($this->systemAdminUser->hasSystemAdminAccess());
        $this->assertTrue($this->systemRootUser->hasSystemAdminAccess());
    }

    // ========================================
    // Cross-Organisation Access Tests
    // ========================================

    public function test_system_admin_can_access_different_organisation(): void
    {
        // Create another organisation
        $anotherOrg = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // System admin should have access to any organisation
        $this->assertTrue($this->systemAdminUser->hasSystemAdminAccess());
        $this->assertTrue($this->systemAdminUser->hasOrganisationAdminAccess($this->organisation->id));
        $this->assertTrue($this->systemAdminUser->hasOrganisationAdminAccess($anotherOrg->id));
    }

    public function test_organisation_owner_cannot_access_different_organisation(): void
    {
        // Create another organisation
        $anotherOrg = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Organisation owner should only have access to their own organisation
        $this->assertFalse($this->ownerUser->hasSystemAdminAccess());
        $this->assertTrue($this->ownerUser->hasOrganisationAdminAccess($this->organisation->id));
        $this->assertFalse($this->ownerUser->hasOrganisationAdminAccess($anotherOrg->id));
    }

    // ========================================
    // Organisation Membership Tests
    // ========================================

    public function test_user_belongs_to_organisation(): void
    {
        $this->assertTrue($this->ownerUser->belongsToOrganisation($this->organisation->id));
        $this->assertTrue($this->memberUser->belongsToOrganisation($this->organisation->id));

        // Create another organisation that users don't belong to
        $anotherOrg = Organisation::factory()->create();
        $this->assertFalse($this->ownerUser->belongsToOrganisation($anotherOrg->id));
        $this->assertFalse($this->memberUser->belongsToOrganisation($anotherOrg->id));
    }

    public function test_user_get_organisation_ids(): void
    {
        // Test single organisation membership
        $this->assertEquals([$this->organisation->id], $this->ownerUser->getOrganisationIds()->toArray());

        // Add user to another organisation
        $anotherOrg = Organisation::factory()->create();
        $this->ownerUser->organisations()->attach($anotherOrg->id);

        $organisationIds = $this->ownerUser->getOrganisationIds()->toArray();
        $this->assertCount(2, $organisationIds);
        $this->assertContains($this->organisation->id, $organisationIds);
        $this->assertContains($anotherOrg->id, $organisationIds);
    }

    // ========================================
    // Role Access Tests
    // ========================================

    public function test_user_can_access_organisation_role(): void
    {
        // Create a test role for the organisation
        $testRole = Role::create([
            'name' => 'test-org-role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->assertTrue($this->ownerUser->canAccessRole($testRole));
        $this->assertTrue($this->memberUser->canAccessRole($testRole));
    }

    public function test_user_cannot_access_organisation_role_without_membership(): void
    {
        // Create another organisation and role
        $anotherOrg = Organisation::factory()->create();
        $otherOrgRole = Role::create([
            'name' => 'other-org-role',
            'guard_name' => 'api',
            'organisation_id' => $anotherOrg->id,
        ]);

        $this->assertFalse($this->ownerUser->canAccessRole($otherOrgRole));
        $this->assertFalse($this->memberUser->canAccessRole($otherOrgRole));
    }

    public function test_user_cannot_access_system_role_without_admin_access(): void
    {
        // Create a system role
        $systemRole = Role::create([
            'name' => 'test-system-role',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Regular organisation users cannot access system roles
        $this->assertFalse($this->ownerUser->canAccessRole($systemRole));
        $this->assertFalse($this->memberUser->canAccessRole($systemRole));
    }

    public function test_system_admin_can_access_system_role(): void
    {
        // Create a system role
        $systemRole = Role::create([
            'name' => 'test-system-role',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Clear team context for system role checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // System admin should be able to access system roles
        $this->assertTrue($this->systemAdminUser->canAccessRole($systemRole));
        $this->assertTrue($this->systemRootUser->canAccessRole($systemRole));
    }

    // ========================================
    // Role Assignment Tests
    // ========================================

    public function test_user_can_assign_role_to_user_in_same_organisation(): void
    {
        // Create a test role for the organisation
        $testRole = Role::create([
            'name' => 'test-org-role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create another user in the same organisation
        $targetUser = User::factory()->create();
        $targetUser->organisations()->attach($this->organisation->id);

        $this->assertTrue($this->ownerUser->canAssignRoleToUser($testRole, $targetUser));
        $this->assertTrue($this->memberUser->canAssignRoleToUser($testRole, $targetUser));
    }

    public function test_user_cannot_assign_role_to_user_in_different_organisation(): void
    {
        // Create a test role for the organisation
        $testRole = Role::create([
            'name' => 'test-org-role',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create another organisation and user
        $anotherOrg = Organisation::factory()->create();
        $targetUser = User::factory()->create();
        $targetUser->organisations()->attach($anotherOrg->id);

        $this->assertFalse($this->ownerUser->canAssignRoleToUser($testRole, $targetUser));
        $this->assertFalse($this->memberUser->canAssignRoleToUser($testRole, $targetUser));
    }

    // ========================================
    // Owner Role Query Tests
    // ========================================

    public function test_has_owner_role_in_any_organisation_returns_true_for_owner(): void
    {
        $this->assertTrue($this->ownerUser->hasOwnerRoleInAnyOrganisation());
    }

    public function test_has_owner_role_in_any_organisation_returns_false_for_member(): void
    {
        $this->assertFalse($this->memberUser->hasOwnerRoleInAnyOrganisation());
    }

    public function test_has_owner_role_in_any_organisation_returns_false_for_system_admin(): void
    {
        $this->assertFalse($this->systemAdminUser->hasOwnerRoleInAnyOrganisation());
        $this->assertFalse($this->systemRootUser->hasOwnerRoleInAnyOrganisation());
    }

    public function test_get_owned_organisation_ids_returns_correct_ids_for_owner(): void
    {
        $ownedIds = $this->ownerUser->getOwnedOrganisationIds();

        $this->assertCount(1, $ownedIds);
        $this->assertTrue($ownedIds->contains($this->organisation->id));
    }

    public function test_get_owned_organisation_ids_returns_empty_for_member(): void
    {
        $ownedIds = $this->memberUser->getOwnedOrganisationIds();

        $this->assertCount(0, $ownedIds);
    }

    public function test_get_owned_organisation_ids_returns_empty_for_system_admin(): void
    {
        $ownedIds = $this->systemAdminUser->getOwnedOrganisationIds();
        $this->assertCount(0, $ownedIds);

        $ownedIds = $this->systemRootUser->getOwnedOrganisationIds();
        $this->assertCount(0, $ownedIds);
    }

    public function test_owner_role_queries_with_multiple_organisations(): void
    {
        // Create additional organisations and assign owner roles
        $org2 = Organisation::factory()->create(['name' => 'Organisation 2']);
        $org3 = Organisation::factory()->create(['name' => 'Organisation 3']);

        // Create owner roles for the new organisations
        $ownerRole2 = Role::create([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $org2->id,
        ]);

        $ownerRole3 = Role::create([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $org3->id,
        ]);

        // Associate user with new organisations
        $this->ownerUser->organisations()->attach([$org2->id, $org3->id]);

        // Assign owner roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($org2->id);
        $this->ownerUser->assignRole($ownerRole2);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($org3->id);
        $this->ownerUser->assignRole($ownerRole3);

        // Test hasOwnerRoleInAnyOrganisation
        $this->assertTrue($this->ownerUser->hasOwnerRoleInAnyOrganisation());

        // Test getOwnedOrganisationIds
        $ownedIds = $this->ownerUser->getOwnedOrganisationIds();
        $this->assertCount(3, $ownedIds);
        $this->assertTrue($ownedIds->contains($this->organisation->id));
        $this->assertTrue($ownedIds->contains($org2->id));
        $this->assertTrue($ownedIds->contains($org3->id));
    }

    public function test_owner_role_queries_performance_with_multiple_organisations(): void
    {
        // Create multiple organisations and assign owner roles
        $organisations = [];
        for ($i = 1; $i <= 5; $i++) {
            $org = Organisation::factory()->create(['name' => "Performance Org {$i}"]);
            $organisations[] = $org;

            $ownerRole = Role::create([
                'name' => 'owner',
                'guard_name' => 'api',
                'organisation_id' => $org->id,
            ]);

            $this->ownerUser->organisations()->attach($org->id);

            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($org->id);
            $this->ownerUser->assignRole($ownerRole);
        }

        // Test with query logging to ensure optimized queries
        \DB::enableQueryLog();

        $hasOwnerRole = $this->ownerUser->hasOwnerRoleInAnyOrganisation();
        $ownedIds = $this->ownerUser->getOwnedOrganisationIds();

        $queryLog = \DB::getQueryLog();
        \DB::disableQueryLog();

        // Verify results
        $this->assertTrue($hasOwnerRole);
        $this->assertCount(6, $ownedIds); // Original org + 5 new orgs

        // Verify query efficiency - should use optimized queries, not N+1
        $this->assertLessThan(10, count($queryLog), 'Owner role queries should be optimized');
    }

    // Product Permission Tests

    public function test_user_product_permissions_relationship(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
        ]);

        $this->assertCount(1, $user->productPermissions);
        $this->assertEquals($product->id, $user->productPermissions->first()->product_id);
    }

    public function test_get_product_permission_ids(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Create valid permissions
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
        ]);

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
        ]);

        // Create expired permission (should not be included)
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product3->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        $productIds = $user->getProductPermissionIds();

        $this->assertCount(2, $productIds);
        $this->assertContains($product1->id, $productIds);
        $this->assertContains($product2->id, $productIds);
        $this->assertNotContains($product3->id, $productIds);
    }

    public function test_has_product_permission(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
        ]);

        $this->assertTrue($user->hasProductPermission($product1->id, 'view-reports'));
        $this->assertFalse($user->hasProductPermission($product2->id, 'view-reports'));
        $this->assertFalse($user->hasProductPermission($product1->id, 'edit-reports'));
    }

    public function test_get_accessible_product_ids_with_product_permissions(): void
    {
        $user = User::factory()->create();
        $organisation = Organisation::factory()->create();
        $product1 = Product::factory()->create(['owner_id' => $organisation->code]);
        $product2 = Product::factory()->create(['owner_id' => $organisation->code]);

        // Grant product permissions
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
        ]);

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
        ]);

        // When user has product permissions, should return only those products
        $accessibleIds = $user->getAccessibleProductIds($organisation->id);

        $this->assertCount(2, $accessibleIds);
        $this->assertContains($product1->id, $accessibleIds);
        $this->assertContains($product2->id, $accessibleIds);
    }

    public function test_get_accessible_product_ids_without_organisation_filter(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        // Grant product permissions
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
        ]);

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
        ]);

        // When no organisation filter, should return all product permissions
        $accessibleIds = $user->getAccessibleProductIds();

        $this->assertCount(2, $accessibleIds);
        $this->assertContains($product1->id, $accessibleIds);
        $this->assertContains($product2->id, $accessibleIds);
    }

    public function test_get_valid_product_permissions(): void
    {
        $user = User::factory()->create();
        $grantedBy = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        // Create valid permission
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
            'granted_by' => $grantedBy->id,
        ]);

        // Create expired permission
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        $validPermissions = $user->getValidProductPermissions();

        $this->assertCount(1, $validPermissions);
        $this->assertEquals($product1->id, $validPermissions->first()->product_id);
        $this->assertEquals($grantedBy->id, $validPermissions->first()->grantedBy->id);
    }

    public function test_system_admin_can_access_all_products(): void
    {
        $organisation = Organisation::factory()->create();
        $product1 = Product::factory()->create(['owner_id' => $organisation->code]);
        $product2 = Product::factory()->create(['owner_id' => $organisation->code]);
        $product3 = Product::factory()->create(); // Different organisation

        // System admin should have access to all products in the specified organisation
        $accessibleIds = $this->systemAdminUser->getAccessibleProductIds($organisation->id);

        $this->assertContains($product1->store_variant_id, $accessibleIds);
        $this->assertContains($product2->store_variant_id, $accessibleIds);
        // Should not contain products from other organisations when organisation filter is applied
        $this->assertNotContains($product3->store_variant_id, $accessibleIds);

        // System admin should have access to all products when no organisation filter
        $allAccessibleIds = $this->systemAdminUser->getAccessibleProductIds();

        $this->assertContains($product1->store_variant_id, $allAccessibleIds);
        $this->assertContains($product2->store_variant_id, $allAccessibleIds);
        $this->assertContains($product3->store_variant_id, $allAccessibleIds);
    }

    public function test_system_root_can_access_all_products(): void
    {
        $organisation = Organisation::factory()->create();
        $product1 = Product::factory()->create(['owner_id' => $organisation->code]);
        $product2 = Product::factory()->create(['owner_id' => $organisation->code]);

        // System root should have access to all products in the specified organisation
        $accessibleIds = $this->systemRootUser->getAccessibleProductIds($organisation->id);

        $this->assertContains($product1->store_variant_id, $accessibleIds);
        $this->assertContains($product2->store_variant_id, $accessibleIds);

        // System root should have access to all products when no organisation filter
        $allAccessibleIds = $this->systemRootUser->getAccessibleProductIds();

        $this->assertContains($product1->store_variant_id, $allAccessibleIds);
        $this->assertContains($product2->store_variant_id, $allAccessibleIds);
    }

    public function test_regular_user_without_permissions_gets_empty_product_list(): void
    {
        $user = User::factory()->create();
        $organisation = Organisation::factory()->create();
        Product::factory()->create(['owner_id' => $organisation->code]);

        // Regular user without any permissions should get empty array
        $accessibleIds = $user->getAccessibleProductIds($organisation->id);

        $this->assertEmpty($accessibleIds);

        // Same for no organisation filter
        $allAccessibleIds = $user->getAccessibleProductIds();

        $this->assertEmpty($allAccessibleIds);
    }
}
